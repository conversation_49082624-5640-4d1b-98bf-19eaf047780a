"""
PDF Text Extraction Module
Supports multiple libraries for robust text extraction from resumes
"""

import PyPDF2
import pdfplumber
import fitz  # PyMuPDF
from pdfminer.high_level import extract_text as pdfminer_extract
import pytesseract
from PIL import Image
import io
import os
import logging
from typing import Dict, List, Optional, Tuple

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PDFTextExtractor:
    """
    Multi-library PDF text extraction with fallback mechanisms
    """

    def __init__(self):
        self.extraction_methods = {
            'pypdf2': self._extract_with_pypdf2,
            'pdfplumber': self._extract_with_pdfplumber,
            'pymupdf': self._extract_with_pymupdf,
            'pdfminer': self._extract_with_pdfminer,
            'ocr': self._extract_with_ocr
        }

    def extract_text(self, pdf_path: str, method: str = 'auto') -> Dict[str, any]:
        """
        Extract text from PDF using specified method or auto-detection

        Args:
            pdf_path: Path to PDF file
            method: Extraction method ('auto', 'pypdf2', 'pdfplumber', 'pymupdf', 'pdfminer', 'ocr')

        Returns:
            Dict with extracted text, method used, and metadata
        """
        if not os.path.exists(pdf_path):
            raise FileNotFoundError(f"PDF file not found: {pdf_path}")

        if method == 'auto':
            return self._auto_extract(pdf_path)
        elif method in self.extraction_methods:
            return self._extract_with_method(pdf_path, method)
        else:
            raise ValueError(f"Unknown extraction method: {method}")

    def _auto_extract(self, pdf_path: str) -> Dict[str, any]:
        """
        Try multiple extraction methods and return the best result
        """
        results = {}

        # Try text-based extraction methods first
        for method in ['pdfplumber', 'pymupdf', 'pypdf2', 'pdfminer']:
            try:
                result = self._extract_with_method(pdf_path, method)
                results[method] = result

                # If we get substantial text, use this method
                if len(result['text'].strip()) > 100:
                    logger.info(f"Successfully extracted text using {method}")
                    return result

            except Exception as e:
                logger.warning(f"Failed to extract with {method}: {str(e)}")
                continue

        # If text extraction fails, try OCR
        try:
            logger.info("Attempting OCR extraction...")
            result = self._extract_with_method(pdf_path, 'ocr')
            return result
        except Exception as e:
            logger.error(f"OCR extraction failed: {str(e)}")

        # Return the best available result
        if results:
            best_method = max(results.keys(), key=lambda k: len(results[k]['text']))
            return results[best_method]

        return {'text': '', 'method': 'failed', 'pages': 0, 'error': 'All extraction methods failed'}

    def _extract_with_method(self, pdf_path: str, method: str) -> Dict[str, any]:
        """Extract text using specific method"""
        return self.extraction_methods[method](pdf_path)

    def _extract_with_pypdf2(self, pdf_path: str) -> Dict[str, any]:
        """Extract text using PyPDF2"""
        text = ""
        pages = 0

        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            pages = len(pdf_reader.pages)

            for page in pdf_reader.pages:
                text += page.extract_text() + "\n"

        return {
            'text': text,
            'method': 'pypdf2',
            'pages': pages,
            'confidence': self._calculate_confidence(text)
        }

    def _extract_with_pdfplumber(self, pdf_path: str) -> Dict[str, any]:
        """Extract text using pdfplumber (better layout preservation)"""
        text = ""
        pages = 0

        with pdfplumber.open(pdf_path) as pdf:
            pages = len(pdf.pages)

            for page in pdf.pages:
                page_text = page.extract_text()
                if page_text:
                    text += page_text + "\n"

        return {
            'text': text,
            'method': 'pdfplumber',
            'pages': pages,
            'confidence': self._calculate_confidence(text)
        }

    def _extract_with_pymupdf(self, pdf_path: str) -> Dict[str, any]:
        """Extract text using PyMuPDF (fast and reliable)"""
        text = ""
        pages = 0

        doc = fitz.open(pdf_path)
        pages = len(doc)

        for page_num in range(pages):
            page = doc.load_page(page_num)
            text += page.get_text() + "\n"

        doc.close()

        return {
            'text': text,
            'method': 'pymupdf',
            'pages': pages,
            'confidence': self._calculate_confidence(text)
        }

    def _extract_with_pdfminer(self, pdf_path: str) -> Dict[str, any]:
        """Extract text using pdfminer.six"""
        text = pdfminer_extract(pdf_path)

        # Count pages by opening with PyMuPDF (lightweight)
        doc = fitz.open(pdf_path)
        pages = len(doc)
        doc.close()

        return {
            'text': text,
            'method': 'pdfminer',
            'pages': pages,
            'confidence': self._calculate_confidence(text)
        }

    def _extract_with_ocr(self, pdf_path: str) -> Dict[str, any]:
        """Extract text using OCR (for scanned PDFs)"""
        text = ""
        pages = 0

        # Convert PDF to images and apply OCR
        doc = fitz.open(pdf_path)
        pages = len(doc)

        for page_num in range(pages):
            page = doc.load_page(page_num)
            pix = page.get_pixmap()
            img_data = pix.tobytes("png")

            # Convert to PIL Image
            image = Image.open(io.BytesIO(img_data))

            # Apply OCR
            page_text = pytesseract.image_to_string(image)
            text += page_text + "\n"

        doc.close()

        return {
            'text': text,
            'method': 'ocr',
            'pages': pages,
            'confidence': self._calculate_confidence(text)
        }

    def _calculate_confidence(self, text: str) -> float:
        """
        Calculate confidence score based on text quality
        """
        if not text.strip():
            return 0.0

        # Basic heuristics for text quality
        words = text.split()
        if len(words) < 10:
            return 0.2

        # Check for common resume keywords
        resume_keywords = ['experience', 'education', 'skills', 'work', 'university',
                          'degree', 'project', 'company', 'developer', 'engineer']

        keyword_count = sum(1 for word in words if word.lower() in resume_keywords)
        keyword_ratio = keyword_count / len(words)

        # Check for email patterns
        email_count = text.count('@')

        # Check for phone patterns
        phone_patterns = ['+', '(', ')', '-']
        phone_indicators = sum(1 for pattern in phone_patterns if pattern in text)

        confidence = min(1.0, (keyword_ratio * 2) + (email_count * 0.1) + (phone_indicators * 0.05))
        return round(confidence, 2)

def test_extraction_methods(pdf_path: str) -> Dict[str, any]:
    """
    Test all extraction methods and compare results
    """
    extractor = PDFTextExtractor()
    results = {}

    for method in ['pypdf2', 'pdfplumber', 'pymupdf', 'pdfminer']:
        try:
            result = extractor.extract_text(pdf_path, method)
            results[method] = {
                'text_length': len(result['text']),
                'confidence': result['confidence'],
                'pages': result['pages'],
                'preview': result['text'][:200] + "..." if len(result['text']) > 200 else result['text']
            }
        except Exception as e:
            results[method] = {'error': str(e)}

    return results

if __name__ == "__main__":
    # Test with a sample PDF
    test_pdf = "resumes/sample_resume.pdf"
    if os.path.exists(test_pdf):
        extractor = PDFTextExtractor()
        result = extractor.extract_text(test_pdf)
        print(f"Extracted {len(result['text'])} characters using {result['method']}")
        print(f"Confidence: {result['confidence']}")
        print(f"Preview: {result['text'][:300]}...")
    else:
        print("No test PDF found. Place a resume PDF in resumes/sample_resume.pdf to test.")