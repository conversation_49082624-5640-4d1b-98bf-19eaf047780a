/**
 * PDF Text Extraction Module
 * Multiple approaches for robust text extraction from resume PDFs
 */

const fs = require('fs');
const path = require('path');
const pdfParse = require('pdf-parse');
const { createWorker } = require('tesseract.js');
const pdf2pic = require('pdf2pic');

class PDFExtractor {
    constructor() {
        this.extractionMethods = {
            'pdf-parse': this.extractWithPdfParse.bind(this),
            'ocr': this.extractWithOCR.bind(this),
            'hybrid': this.extractWithHybrid.bind(this)
        };
    }

    /**
     * Main extraction method with auto-detection
     * @param {string} pdfPath - Path to PDF file
     * @param {string} method - Extraction method ('auto', 'pdf-parse', 'ocr', 'hybrid')
     * @returns {Object} Extraction result
     */
    async extractText(pdfPath, method = 'auto') {
        if (!fs.existsSync(pdfPath)) {
            throw new Error(`PDF file not found: ${pdfPath}`);
        }

        console.log(`📄 Extracting text from ${path.basename(pdfPath)} using ${method} method...`);

        if (method === 'auto') {
            return await this.autoExtract(pdfPath);
        }

        if (!this.extractionMethods[method]) {
            throw new Error(`Unknown extraction method: ${method}`);
        }

        return await this.extractionMethods[method](pdfPath);
    }

    /**
     * Auto-detection: try multiple methods and return best result
     */
    async autoExtract(pdfPath) {
        const results = {};

        // Try PDF parsing first (fastest)
        try {
            console.log('🔍 Trying PDF parsing...');
            const pdfResult = await this.extractWithPdfParse(pdfPath);
            results['pdf-parse'] = pdfResult;

            // If we get substantial text, use this method
            if (pdfResult.text.trim().length > 100) {
                console.log('✅ PDF parsing successful');
                return pdfResult;
            }
        } catch (error) {
            console.log('⚠️ PDF parsing failed:', error.message);
        }

        // If PDF parsing fails or gives poor results, try OCR
        try {
            console.log('🔍 Trying OCR extraction...');
            const ocrResult = await this.extractWithOCR(pdfPath);
            results['ocr'] = ocrResult;
            console.log('✅ OCR extraction completed');
            return ocrResult;
        } catch (error) {
            console.log('❌ OCR extraction failed:', error.message);
        }

        // Return best available result
        const bestMethod = Object.keys(results).reduce((a, b) => 
            results[a].text.length > results[b].text.length ? a : b
        );

        return results[bestMethod] || {
            text: '',
            method: 'failed',
            pages: 0,
            confidence: 0,
            error: 'All extraction methods failed'
        };
    }

    /**
     * Extract text using pdf-parse library
     */
    async extractWithPdfParse(pdfPath) {
        const dataBuffer = fs.readFileSync(pdfPath);
        const data = await pdfParse(dataBuffer);

        return {
            text: data.text,
            method: 'pdf-parse',
            pages: data.numpages,
            confidence: this.calculateConfidence(data.text),
            metadata: {
                info: data.info,
                version: data.version
            }
        };
    }

    /**
     * Extract text using OCR (Tesseract.js)
     */
    async extractWithOCR(pdfPath) {
        console.log('🖼️ Converting PDF to images for OCR...');
        
        // Convert PDF to images
        const convert = pdf2pic.fromPath(pdfPath, {
            density: 300,
            saveFilename: "page",
            savePath: "./temp",
            format: "png",
            width: 2000,
            height: 2000
        });

        let allText = '';
        let pageCount = 0;

        try {
            // Get first few pages (limit for performance)
            const maxPages = 5;
            
            for (let i = 1; i <= maxPages; i++) {
                try {
                    console.log(`📄 Processing page ${i}...`);
                    const result = await convert(i);
                    
                    if (result && result.path) {
                        const worker = await createWorker();
                        await worker.loadLanguage('eng');
                        await worker.initialize('eng');
                        
                        const { data: { text } } = await worker.recognize(result.path);
                        allText += text + '\n';
                        pageCount++;
                        
                        await worker.terminate();
                        
                        // Clean up temp image
                        if (fs.existsSync(result.path)) {
                            fs.unlinkSync(result.path);
                        }
                    }
                } catch (pageError) {
                    console.log(`⚠️ Failed to process page ${i}:`, pageError.message);
                    break; // Stop if we can't process more pages
                }
            }

            return {
                text: allText,
                method: 'ocr',
                pages: pageCount,
                confidence: this.calculateConfidence(allText)
            };

        } catch (error) {
            throw new Error(`OCR extraction failed: ${error.message}`);
        }
    }

    /**
     * Hybrid approach: combine PDF parsing and OCR
     */
    async extractWithHybrid(pdfPath) {
        console.log('🔄 Using hybrid extraction approach...');
        
        let pdfResult = null;
        let ocrResult = null;

        // Try PDF parsing first
        try {
            pdfResult = await this.extractWithPdfParse(pdfPath);
        } catch (error) {
            console.log('⚠️ PDF parsing failed in hybrid mode');
        }

        // If PDF parsing gives poor results, supplement with OCR
        if (!pdfResult || pdfResult.confidence < 0.5) {
            try {
                ocrResult = await this.extractWithOCR(pdfPath);
            } catch (error) {
                console.log('⚠️ OCR failed in hybrid mode');
            }
        }

        // Choose best result or combine
        if (pdfResult && ocrResult) {
            // Combine results if both are available
            const combinedText = pdfResult.confidence > ocrResult.confidence ? 
                pdfResult.text : ocrResult.text;
            
            return {
                text: combinedText,
                method: 'hybrid',
                pages: Math.max(pdfResult.pages, ocrResult.pages),
                confidence: Math.max(pdfResult.confidence, ocrResult.confidence),
                details: {
                    pdfConfidence: pdfResult.confidence,
                    ocrConfidence: ocrResult.confidence,
                    selectedMethod: pdfResult.confidence > ocrResult.confidence ? 'pdf-parse' : 'ocr'
                }
            };
        }

        return pdfResult || ocrResult || {
            text: '',
            method: 'hybrid-failed',
            pages: 0,
            confidence: 0,
            error: 'Both PDF parsing and OCR failed'
        };
    }

    /**
     * Calculate confidence score based on text quality
     */
    calculateConfidence(text) {
        if (!text || text.trim().length === 0) {
            return 0;
        }

        const words = text.split(/\s+/).filter(word => word.length > 0);
        if (words.length < 10) {
            return 0.2;
        }

        // Check for resume-specific keywords
        const resumeKeywords = [
            'experience', 'education', 'skills', 'work', 'university', 'degree',
            'project', 'company', 'developer', 'engineer', 'manager', 'analyst',
            'javascript', 'python', 'java', 'react', 'angular', 'node'
        ];

        const keywordCount = resumeKeywords.reduce((count, keyword) => {
            return count + (text.toLowerCase().includes(keyword) ? 1 : 0);
        }, 0);

        const keywordRatio = keywordCount / resumeKeywords.length;

        // Check for email patterns
        const emailCount = (text.match(/@/g) || []).length;

        // Check for phone patterns
        const phonePatterns = /(\+?\d{1,3}[-.\s]?\d{10}|\(\d{3}\)\s?\d{3}[-.\s]?\d{4})/g;
        const phoneCount = (text.match(phonePatterns) || []).length;

        // Calculate confidence
        let confidence = 0.3; // Base confidence
        confidence += keywordRatio * 0.4; // Keyword presence
        confidence += Math.min(emailCount * 0.15, 0.15); // Email presence
        confidence += Math.min(phoneCount * 0.15, 0.15); // Phone presence

        return Math.min(1.0, Math.round(confidence * 100) / 100);
    }

    /**
     * Test all extraction methods on a PDF
     */
    async testAllMethods(pdfPath) {
        console.log(`🧪 Testing all extraction methods on ${path.basename(pdfPath)}`);
        
        const results = {};
        
        for (const [methodName, method] of Object.entries(this.extractionMethods)) {
            try {
                console.log(`\n🔍 Testing ${methodName}...`);
                const startTime = Date.now();
                const result = await method(pdfPath);
                const duration = Date.now() - startTime;
                
                results[methodName] = {
                    success: true,
                    textLength: result.text.length,
                    confidence: result.confidence,
                    pages: result.pages,
                    duration: duration,
                    preview: result.text.substring(0, 200) + '...'
                };
                
                console.log(`✅ ${methodName}: ${result.text.length} chars, confidence: ${result.confidence}`);
                
            } catch (error) {
                results[methodName] = {
                    success: false,
                    error: error.message
                };
                console.log(`❌ ${methodName} failed: ${error.message}`);
            }
        }
        
        return results;
    }
}

module.exports = PDFExtractor;

// Create temp directory if it doesn't exist
const tempDir = path.join(__dirname, 'temp');
if (!fs.existsSync(tempDir)) {
    fs.mkdirSync(tempDir, { recursive: true });
}
