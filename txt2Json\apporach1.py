"""
Approach 1: Rule-based Pattern Matching for Resume Parsing
Uses regex patterns and heuristics to extract structured data from resume text
"""

import re
import json
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RuleBasedResumeParser:
    """
    Rule-based resume parser using regex patterns and heuristics
    """

    def __init__(self):
        self.setup_patterns()
        self.setup_skill_keywords()
        self.setup_section_keywords()

    def setup_patterns(self):
        """Initialize regex patterns for different entities"""

        # Email pattern
        self.email_pattern = re.compile(
            r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        )

        # Phone patterns (multiple formats)
        self.phone_patterns = [
            re.compile(r'\+\d{1,3}[-.\s]?\d{10}'),  # +91 9876543210
            re.compile(r'\(\d{3}\)\s?\d{3}[-.\s]?\d{4}'),  # (*************
            re.compile(r'\d{3}[-.\s]?\d{3}[-.\s]?\d{4}'),  # ************
            re.compile(r'\d{10}'),  # 9876543210
            re.compile(r'\+\d{1,3}\s?\d{10}'),  # +91 9876543210
        ]

        # Name patterns (first line, before email, etc.)
        self.name_pattern = re.compile(r'^([A-Z][a-z]+\s+[A-Z][a-z]+(?:\s+[A-Z][a-z]+)?)', re.MULTILINE)

        # Date patterns
        self.date_patterns = [
            re.compile(r'(\d{1,2})[/-](\d{1,2})[/-](\d{4})'),  # DD/MM/YYYY or MM/DD/YYYY
            re.compile(r'(\d{4})[/-](\d{1,2})[/-](\d{1,2})'),  # YYYY/MM/DD
            re.compile(r'(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\s+(\d{4})', re.IGNORECASE),
            re.compile(r'(\d{1,2})\s+(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\s+(\d{4})', re.IGNORECASE),
            re.compile(r'(\d{4})'),  # Just year
        ]

        # Address patterns
        self.address_patterns = [
            re.compile(r'\b\d{5,6}\b'),  # Pincode
            re.compile(r'\b(India|USA|UK|Canada|Australia)\b', re.IGNORECASE),  # Country
        ]

        # Education patterns
        self.education_patterns = [
            re.compile(r'(B\.?Tech|Bachelor|B\.?E\.?|B\.?Sc|M\.?Tech|Master|M\.?E\.?|M\.?Sc|PhD|Doctorate)', re.IGNORECASE),
            re.compile(r'(University|College|Institute|School)', re.IGNORECASE),
            re.compile(r'(Computer Science|Information Technology|Software|Engineering)', re.IGNORECASE),
        ]

        # Experience patterns
        self.experience_patterns = [
            re.compile(r'(\d+)\s*(years?|yrs?)\s*(of\s*)?(experience|exp)', re.IGNORECASE),
            re.compile(r'(Software Engineer|Developer|Programmer|Analyst)', re.IGNORECASE),
            re.compile(r'(Company|Corp|Ltd|Inc|Technologies|Solutions)', re.IGNORECASE),
        ]

    def setup_skill_keywords(self):
        """Setup skill keyword categories"""
        self.skill_categories = {
            'frontend': [
                'angular', 'react', 'vue', 'javascript', 'typescript', 'html', 'css', 'scss', 'sass',
                'bootstrap', 'material-ui', 'jquery', 'webpack', 'npm', 'yarn'
            ],
            'backend': [
                'node.js', 'express', 'python', 'django', 'flask', 'java', 'spring', 'c#', '.net',
                'php', 'laravel', 'ruby', 'rails', 'go', 'rust'
            ],
            'database': [
                'mysql', 'postgresql', 'mongodb', 'redis', 'sqlite', 'oracle', 'sql server',
                'dynamodb', 'cassandra', 'elasticsearch'
            ],
            'cloud': [
                'aws', 'azure', 'gcp', 'docker', 'kubernetes', 'jenkins', 'ci/cd', 'terraform',
                'ansible', 'nginx', 'apache'
            ],
            'tools': [
                'git', 'github', 'gitlab', 'jira', 'confluence', 'slack', 'visual studio code',
                'intellij', 'postman', 'swagger'
            ]
        }

        # Flatten all skills for general matching
        self.all_skills = []
        for category_skills in self.skill_categories.values():
            self.all_skills.extend(category_skills)

    def setup_section_keywords(self):
        """Setup section header keywords"""
        self.section_keywords = {
            'experience': ['experience', 'work experience', 'employment', 'career', 'professional experience'],
            'education': ['education', 'academic', 'qualification', 'degree'],
            'skills': ['skills', 'technical skills', 'technologies', 'expertise', 'competencies'],
            'projects': ['projects', 'personal projects', 'work projects'],
            'contact': ['contact', 'personal information', 'details'],
            'summary': ['summary', 'objective', 'profile', 'about']
        }

    def parse_resume(self, text: str) -> Dict[str, Any]:
        """
        Main parsing function that extracts all entities from resume text

        Args:
            text: Raw resume text

        Returns:
            Structured JSON data
        """
        logger.info("Starting rule-based resume parsing...")

        # Initialize result structure
        result = {
            "resume_id": str(uuid.uuid4()),
            "address": {
                "country": "",
                "state": "",
                "city": "",
                "pincode": ""
            },
            "name": "",
            "email": "",
            "phone": "",
            "skills": [],
            "education": [],
            "experience": [],
            "parsing_metadata": {
                "method": "rule_based",
                "timestamp": datetime.now().isoformat(),
                "confidence_scores": {}
            }
        }

        # Extract different entities
        result["name"] = self.extract_name(text)
        result["email"] = self.extract_email(text)
        result["phone"] = self.extract_phone(text)
        result["address"] = self.extract_address(text)
        result["skills"] = self.extract_skills(text)
        result["education"] = self.extract_education(text)
        result["experience"] = self.extract_experience(text)

        # Calculate confidence scores
        result["parsing_metadata"]["confidence_scores"] = self.calculate_confidence_scores(result)

        logger.info(f"Parsing completed. Extracted: {len(result['skills'])} skills, {len(result['education'])} education entries, {len(result['experience'])} experience entries")

        return result

    def extract_name(self, text: str) -> str:
        """Extract candidate name from text"""
        lines = text.strip().split('\n')

        # Try first non-empty line
        for line in lines[:5]:  # Check first 5 lines
            line = line.strip()
            if line and len(line.split()) >= 2 and len(line.split()) <= 4:
                # Check if it looks like a name (no numbers, not too long)
                if not re.search(r'\d', line) and len(line) < 50:
                    # Check if it's not an email or phone
                    if '@' not in line and not any(pattern.search(line) for pattern in self.phone_patterns):
                        return line.title()

        # Fallback: use regex pattern
        match = self.name_pattern.search(text)
        if match:
            return match.group(1).title()

        return ""

    def extract_email(self, text: str) -> str:
        """Extract email address from text"""
        match = self.email_pattern.search(text)
        return match.group(0) if match else ""

    def extract_phone(self, text: str) -> str:
        """Extract phone number from text"""
        for pattern in self.phone_patterns:
            match = pattern.search(text)
            if match:
                return match.group(0)
        return ""

    def extract_address(self, text: str) -> Dict[str, str]:
        """Extract address components from text"""
        address = {
            "country": "",
            "state": "",
            "city": "",
            "pincode": ""
        }

        # Extract pincode
        pincode_match = re.search(r'\b\d{5,6}\b', text)
        if pincode_match:
            address["pincode"] = pincode_match.group(0)

        # Extract country
        country_match = re.search(r'\b(India|USA|United States|UK|United Kingdom|Canada|Australia)\b', text, re.IGNORECASE)
        if country_match:
            address["country"] = country_match.group(0)

        # Extract common Indian cities
        indian_cities = ['Mumbai', 'Delhi', 'Bangalore', 'Hyderabad', 'Chennai', 'Kolkata', 'Pune', 'Ahmedabad', 'Jaipur', 'Lucknow']
        for city in indian_cities:
            if re.search(rf'\b{city}\b', text, re.IGNORECASE):
                address["city"] = city
                break

        return address

    def extract_skills(self, text: str) -> List[str]:
        """Extract skills from text using keyword matching"""
        found_skills = []
        text_lower = text.lower()

        # Check for each skill in our database
        for skill in self.all_skills:
            # Use word boundaries to avoid partial matches
            pattern = rf'\b{re.escape(skill.lower())}\b'
            if re.search(pattern, text_lower):
                found_skills.append(skill.title())

        # Remove duplicates and sort
        return sorted(list(set(found_skills)))

    def extract_education(self, text: str) -> List[str]:
        """Extract education information from text"""
        education_entries = []
        lines = text.split('\n')

        # Look for education section
        education_section = self.find_section(text, 'education')
        if education_section:
            lines = education_section.split('\n')

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # Check if line contains education keywords
            has_degree = any(pattern.search(line) for pattern in self.education_patterns[:1])  # Degree patterns
            has_institution = any(pattern.search(line) for pattern in self.education_patterns[1:2])  # Institution patterns

            if has_degree or has_institution:
                # Clean and format the education entry
                cleaned_line = re.sub(r'\s+', ' ', line).strip()
                if len(cleaned_line) > 10 and cleaned_line not in education_entries:
                    education_entries.append(cleaned_line)

        return education_entries[:5]  # Limit to 5 entries

    def extract_experience(self, text: str) -> List[Dict[str, Any]]:
        """Extract work experience from text"""
        experience_entries = []

        # Look for experience section
        experience_section = self.find_section(text, 'experience')
        if experience_section:
            text = experience_section

        lines = text.split('\n')
        current_entry = {}

        for i, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue

            # Check if line contains job title
            job_title_match = re.search(r'(Software Engineer|Developer|Programmer|Analyst|Manager|Lead|Senior|Junior)', line, re.IGNORECASE)
            if job_title_match:
                # Save previous entry if exists
                if current_entry:
                    experience_entries.append(current_entry)

                # Start new entry
                current_entry = {
                    "company": "",
                    "role": line.strip(),
                    "start_date": {"year": 0, "month": 0, "day": 0},
                    "end_date": {"year": 0, "month": 0, "day": 0},
                    "description": "",
                    "responsibilities": []
                }

                # Look for company in next few lines
                for j in range(i+1, min(i+4, len(lines))):
                    next_line = lines[j].strip()
                    if any(pattern.search(next_line) for pattern in self.experience_patterns[2:]):  # Company patterns
                        current_entry["company"] = next_line
                        break

                # Extract dates from current line or nearby lines
                dates = self.extract_dates_from_text(line)
                if dates and len(dates) >= 2:
                    current_entry["start_date"] = dates[0]
                    current_entry["end_date"] = dates[1]
                elif dates and len(dates) == 1:
                    current_entry["start_date"] = dates[0]

        # Add last entry
        if current_entry:
            experience_entries.append(current_entry)

        return experience_entries[:5]  # Limit to 5 entries

    def find_section(self, text: str, section_type: str) -> str:
        """Find and extract a specific section from resume text"""
        if section_type not in self.section_keywords:
            return ""

        keywords = self.section_keywords[section_type]
        lines = text.split('\n')

        section_start = -1
        section_end = len(lines)

        # Find section start
        for i, line in enumerate(lines):
            line_lower = line.lower().strip()
            if any(keyword in line_lower for keyword in keywords):
                section_start = i + 1
                break

        if section_start == -1:
            return ""

        # Find section end (next section or end of text)
        for i in range(section_start, len(lines)):
            line_lower = lines[i].lower().strip()
            # Check if this line starts a new section
            for other_section, other_keywords in self.section_keywords.items():
                if other_section != section_type:
                    if any(keyword in line_lower for keyword in other_keywords):
                        section_end = i
                        break
            if section_end != len(lines):
                break

        return '\n'.join(lines[section_start:section_end])

    def extract_dates_from_text(self, text: str) -> List[Dict[str, int]]:
        """Extract dates from text and convert to structured format"""
        dates = []

        for pattern in self.date_patterns:
            matches = pattern.findall(text)
            for match in matches:
                if isinstance(match, tuple):
                    if len(match) == 3:  # DD/MM/YYYY or similar
                        try:
                            if len(match[2]) == 4:  # Year is last
                                year = int(match[2])
                                month = int(match[1]) if match[1].isdigit() else 1
                                day = int(match[0]) if match[0].isdigit() else 1
                            else:  # YYYY/MM/DD
                                year = int(match[0])
                                month = int(match[1]) if match[1].isdigit() else 1
                                day = int(match[2]) if match[2].isdigit() else 1

                            dates.append({"year": year, "month": month, "day": day})
                        except ValueError:
                            continue
                    elif len(match) == 2:  # Month Year
                        try:
                            month_name = match[0] if not match[0].isdigit() else match[1]
                            year = int(match[1]) if match[1].isdigit() else int(match[0])

                            month_map = {
                                'jan': 1, 'feb': 2, 'mar': 3, 'apr': 4, 'may': 5, 'jun': 6,
                                'jul': 7, 'aug': 8, 'sep': 9, 'oct': 10, 'nov': 11, 'dec': 12
                            }
                            month = month_map.get(month_name.lower()[:3], 1)

                            dates.append({"year": year, "month": month, "day": 1})
                        except ValueError:
                            continue
                else:  # Just year
                    try:
                        year = int(match)
                        if 1900 <= year <= 2030:
                            dates.append({"year": year, "month": 1, "day": 1})
                    except ValueError:
                        continue

        return dates

    def calculate_confidence_scores(self, result: Dict[str, Any]) -> Dict[str, float]:
        """Calculate confidence scores for extracted data"""
        scores = {}

        # Name confidence
        scores["name"] = 1.0 if result["name"] else 0.0

        # Email confidence
        scores["email"] = 1.0 if result["email"] else 0.0

        # Phone confidence
        scores["phone"] = 1.0 if result["phone"] else 0.0

        # Address confidence
        address_fields = [v for v in result["address"].values() if v]
        scores["address"] = len(address_fields) / 4.0

        # Skills confidence
        scores["skills"] = min(1.0, len(result["skills"]) / 10.0)

        # Education confidence
        scores["education"] = min(1.0, len(result["education"]) / 3.0)

        # Experience confidence
        scores["experience"] = min(1.0, len(result["experience"]) / 3.0)

        # Overall confidence
        scores["overall"] = sum(scores.values()) / len(scores)

        return {k: round(v, 2) for k, v in scores.items()}

def test_rule_based_parser():
    """Test the rule-based parser with sample text"""
    sample_text = """
    John Doe
    Software Engineer
    <EMAIL>
    +91 9876543210

    Experience:
    Senior Angular Developer at Tech Solutions Pvt Ltd
    June 2021 - March 2023
    - Developed Angular applications
    - Implemented TypeScript features

    Education:
    B.Tech in Computer Science, XYZ University, 2020

    Skills:
    Angular, TypeScript, JavaScript, Node.js, MongoDB
    """

    parser = RuleBasedResumeParser()
    result = parser.parse_resume(sample_text)

    print("Rule-based Parser Results:")
    print(json.dumps(result, indent=2))
    return result

if __name__ == "__main__":
    test_rule_based_parser()