"""
Approach 3: Machine Learning-based Resume Parsing
Uses scikit-learn for text classification and entity extraction
"""

import re
import json
import uuid
import numpy as np
from datetime import datetime
from typing import Dict, List, Any, Tuple
import logging
from collections import defaultdict, Counter

# Scikit-learn imports
from sklearn.feature_extraction.text import TfidfVectorizer, CountVectorizer
from sklearn.naive_bayes import MultinomialNB
from sklearn.linear_model import LogisticRegression
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, accuracy_score
from sklearn.pipeline import Pipeline
import pickle
import os

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MLResumeParser:
    """
    Machine Learning-based resume parser using scikit-learn
    """
    
    def __init__(self):
        self.section_classifier = None
        self.entity_extractors = {}
        self.vectorizers = {}
        self.setup_training_data()
        self.train_models()
    
    def setup_training_data(self):
        """Setup training data for section classification and entity extraction"""
        
        # Section classification training data
        self.section_training_data = [
            # Contact/Personal Information
            ("<PERSON>\nSoftware Engineer\<EMAIL>\n+91 9876543210", "contact"),
            ("Name: Jane Smith\nEmail: <EMAIL>\nPhone: (*************", "contact"),
            ("Contact Information\nMichael Johnson\<EMAIL>\n***************", "contact"),
            
            # Experience
            ("Work Experience\nSoftware Engineer at Google\n2020-2023\nDeveloped web applications", "experience"),
            ("Professional Experience\nSenior Developer\nMicrosoft Corporation\nJune 2019 - Present", "experience"),
            ("Employment History\nFrontend Developer at Startup Inc\n2018-2020\nBuilt React applications", "experience"),
            
            # Education
            ("Education\nB.Tech Computer Science\nIIT Delhi\n2016-2020", "education"),
            ("Academic Qualifications\nMaster of Science in Software Engineering\nStanford University", "education"),
            ("Degree: Bachelor of Engineering\nUniversity of California\nGraduation: 2018", "education"),
            
            # Skills
            ("Technical Skills\nJavaScript, Python, React, Angular, Node.js", "skills"),
            ("Skills and Technologies\nProgramming: Java, C++, Python\nFrameworks: Spring, Django", "skills"),
            ("Core Competencies\nWeb Development, Database Design, Cloud Computing", "skills"),
            
            # Projects
            ("Projects\nE-commerce Website\nBuilt using React and Node.js", "projects"),
            ("Personal Projects\nMobile App Development\nCreated iOS app using Swift", "projects"),
            ("Academic Projects\nMachine Learning Model\nDeveloped predictive analytics system", "projects"),
        ]
        
        # Entity patterns for training
        self.entity_patterns = {
            'email': [
                r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
            ],
            'phone': [
                r'\+\d{1,3}[-.\s]?\d{10}',
                r'\(\d{3}\)\s?\d{3}[-.\s]?\d{4}',
                r'\d{3}[-.\s]?\d{3}[-.\s]?\d{4}',
                r'\d{10}',
            ],
            'name': [
                r'^[A-Z][a-z]+\s+[A-Z][a-z]+(?:\s+[A-Z][a-z]+)?$',
            ],
            'year': [
                r'\b(19|20)\d{2}\b',
            ],
            'degree': [
                r'\b(B\.?Tech|Bachelor|B\.?E\.?|B\.?Sc|M\.?Tech|Master|M\.?E\.?|M\.?Sc|PhD)\b',
            ],
            'company': [
                r'\b\w+\s+(Corp|Corporation|Inc|Ltd|Limited|Technologies|Solutions|Systems)\b',
            ]
        }
        
        # Skill keywords database
        self.skill_keywords = {
            'programming': ['python', 'java', 'javascript', 'typescript', 'c++', 'c#', 'php', 'ruby', 'go'],
            'frontend': ['react', 'angular', 'vue', 'html', 'css', 'bootstrap', 'jquery'],
            'backend': ['node.js', 'express', 'django', 'flask', 'spring', 'asp.net'],
            'database': ['mysql', 'postgresql', 'mongodb', 'redis', 'oracle', 'sqlite'],
            'cloud': ['aws', 'azure', 'gcp', 'docker', 'kubernetes'],
            'tools': ['git', 'jenkins', 'jira', 'postman', 'webpack']
        }
        
        # Flatten skills for easier processing
        self.all_skills = []
        for category_skills in self.skill_keywords.values():
            self.all_skills.extend(category_skills)
    
    def train_models(self):
        """Train machine learning models for section classification"""
        logger.info("Training ML models...")
        
        # Prepare section classification data
        texts = [item[0] for item in self.section_training_data]
        labels = [item[1] for item in self.section_training_data]
        
        # Create and train section classifier
        self.section_classifier = Pipeline([
            ('tfidf', TfidfVectorizer(max_features=1000, stop_words='english')),
            ('classifier', MultinomialNB())
        ])
        
        self.section_classifier.fit(texts, labels)
        
        # Train entity extractors (simple pattern-based for now)
        self.train_entity_extractors()
        
        logger.info("ML models trained successfully")
    
    def train_entity_extractors(self):
        """Train entity extraction models"""
        # For this implementation, we'll use pattern-based extraction
        # In a real scenario, you would train NER models with labeled data
        
        self.entity_extractors = {
            'email': self.extract_email_ml,
            'phone': self.extract_phone_ml,
            'name': self.extract_name_ml,
            'skills': self.extract_skills_ml,
            'dates': self.extract_dates_ml,
            'companies': self.extract_companies_ml,
            'degrees': self.extract_degrees_ml
        }
    
    def parse_resume(self, text: str) -> Dict[str, Any]:
        """
        Main parsing function using ML approach
        
        Args:
            text: Raw resume text
            
        Returns:
            Structured JSON data
        """
        logger.info("Starting ML-based resume parsing...")
        
        # Initialize result structure
        result = {
            "resume_id": str(uuid.uuid4()),
            "address": {
                "country": "",
                "state": "",
                "city": "",
                "pincode": ""
            },
            "name": "",
            "email": "",
            "phone": "",
            "skills": [],
            "education": [],
            "experience": [],
            "parsing_metadata": {
                "method": "machine_learning",
                "timestamp": datetime.now().isoformat(),
                "confidence_scores": {},
                "sections_detected": {}
            }
        }
        
        # Split text into sections
        sections = self.segment_text_into_sections(text)
        result["parsing_metadata"]["sections_detected"] = sections
        
        # Extract entities using ML models
        result["name"] = self.entity_extractors['name'](text)
        result["email"] = self.entity_extractors['email'](text)
        result["phone"] = self.entity_extractors['phone'](text)
        result["skills"] = self.entity_extractors['skills'](text)
        result["education"] = self.extract_education_ml(text, sections)
        result["experience"] = self.extract_experience_ml(text, sections)
        result["address"] = self.extract_address_ml(text)
        
        # Calculate confidence scores
        result["parsing_metadata"]["confidence_scores"] = self.calculate_confidence_scores_ml(result)
        
        logger.info(f"ML parsing completed. Detected {len(sections)} sections, extracted {len(result['skills'])} skills")
        
        return result
    
    def segment_text_into_sections(self, text: str) -> Dict[str, str]:
        """Segment text into different resume sections using ML classification"""
        sections = {}
        
        # Split text into paragraphs
        paragraphs = [p.strip() for p in text.split('\n\n') if p.strip()]
        
        for i, paragraph in enumerate(paragraphs):
            if len(paragraph) > 10:  # Skip very short paragraphs
                try:
                    # Predict section type
                    predicted_section = self.section_classifier.predict([paragraph])[0]
                    confidence = max(self.section_classifier.predict_proba([paragraph])[0])
                    
                    # Only accept predictions with reasonable confidence
                    if confidence > 0.3:
                        if predicted_section not in sections:
                            sections[predicted_section] = paragraph
                        else:
                            sections[predicted_section] += "\n" + paragraph
                    else:
                        sections[f"unknown_{i}"] = paragraph
                        
                except Exception as e:
                    logger.warning(f"Error classifying paragraph: {e}")
                    sections[f"error_{i}"] = paragraph
        
        return sections
    
    def extract_email_ml(self, text: str) -> str:
        """Extract email using pattern matching"""
        for pattern in self.entity_patterns['email']:
            match = re.search(pattern, text)
            if match:
                return match.group(0)
        return ""
    
    def extract_phone_ml(self, text: str) -> str:
        """Extract phone using pattern matching"""
        for pattern in self.entity_patterns['phone']:
            match = re.search(pattern, text)
            if match:
                return match.group(0)
        return ""
    
    def extract_name_ml(self, text: str) -> str:
        """Extract name using heuristics and patterns"""
        lines = text.strip().split('\n')
        
        # Try first few lines
        for line in lines[:5]:
            line = line.strip()
            if line and len(line.split()) >= 2 and len(line.split()) <= 4:
                # Check if it looks like a name
                if not re.search(r'[@\d]', line) and len(line) < 50:
                    # Additional checks
                    words = line.split()
                    if all(word[0].isupper() for word in words if word):
                        return line.title()
        
        return ""
    
    def extract_skills_ml(self, text: str) -> List[str]:
        """Extract skills using keyword matching and ML features"""
        found_skills = set()
        text_lower = text.lower()
        
        # Direct keyword matching
        for skill in self.all_skills:
            if re.search(rf'\b{re.escape(skill.lower())}\b', text_lower):
                found_skills.add(skill.title())
        
        # Use TF-IDF to find important terms that might be skills
        try:
            vectorizer = TfidfVectorizer(max_features=100, stop_words='english')
            tfidf_matrix = vectorizer.fit_transform([text])
            feature_names = vectorizer.get_feature_names_out()
            scores = tfidf_matrix.toarray()[0]
            
            # Get top scoring terms
            top_indices = np.argsort(scores)[-20:]  # Top 20 terms
            for idx in top_indices:
                term = feature_names[idx]
                if len(term) > 2 and term not in ['experience', 'work', 'project']:
                    # Check if term might be a skill
                    if any(skill_cat in term.lower() for skill_cat in ['dev', 'prog', 'tech', 'web']):
                        found_skills.add(term.title())
        
        except Exception as e:
            logger.warning(f"Error in TF-IDF skill extraction: {e}")
        
        return sorted(list(found_skills))

    def extract_dates_ml(self, text: str) -> List[Dict[str, int]]:
        """Extract dates using pattern matching"""
        dates = []

        for pattern in self.entity_patterns['year']:
            matches = re.findall(pattern, text)
            for match in matches:
                try:
                    year = int(match)
                    if 1990 <= year <= 2030:
                        dates.append({"year": year, "month": 1, "day": 1})
                except ValueError:
                    continue

        return dates

    def extract_companies_ml(self, text: str) -> List[str]:
        """Extract company names using patterns"""
        companies = []

        for pattern in self.entity_patterns['company']:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                if isinstance(match, tuple):
                    company = ' '.join(match)
                else:
                    company = match
                companies.append(company.strip())

        return list(set(companies))

    def extract_degrees_ml(self, text: str) -> List[str]:
        """Extract degree information using patterns"""
        degrees = []

        for pattern in self.entity_patterns['degree']:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                degrees.append(match)

        return list(set(degrees))

    def extract_education_ml(self, text: str, sections: Dict[str, str]) -> List[str]:
        """Extract education information using ML and patterns"""
        education_entries = []

        # Look for education section first
        education_text = sections.get('education', '')
        if not education_text:
            # Fallback to full text
            education_text = text

        # Extract degrees
        degrees = self.extract_degrees_ml(education_text)

        # Extract years
        dates = self.extract_dates_ml(education_text)

        # Look for university/college names
        university_patterns = [
            r'\b\w+\s+(University|College|Institute|School)\b',
            r'\b(IIT|NIT|BITS|MIT|Stanford|Harvard|Berkeley)\b'
        ]

        universities = []
        for pattern in university_patterns:
            matches = re.findall(pattern, education_text, re.IGNORECASE)
            universities.extend(matches)

        # Combine information
        if degrees or universities or dates:
            education_entry = ""
            if degrees:
                education_entry += f"{degrees[0]} "
            if universities:
                if isinstance(universities[0], tuple):
                    education_entry += f"from {' '.join(universities[0])} "
                else:
                    education_entry += f"from {universities[0]} "
            if dates:
                education_entry += f"({dates[0]['year']})"

            if education_entry.strip():
                education_entries.append(education_entry.strip())

        # Also look for complete education lines
        lines = education_text.split('\n')
        for line in lines:
            line = line.strip()
            if len(line) > 20 and any(keyword in line.lower() for keyword in ['university', 'college', 'degree', 'bachelor', 'master']):
                if line not in education_entries:
                    education_entries.append(line)

        return education_entries[:3]  # Limit to 3 entries

    def extract_experience_ml(self, text: str, sections: Dict[str, str]) -> List[Dict[str, Any]]:
        """Extract work experience using ML and patterns"""
        experience_entries = []

        # Look for experience section first
        experience_text = sections.get('experience', '')
        if not experience_text:
            experience_text = text

        # Extract companies
        companies = self.extract_companies_ml(experience_text)

        # Extract dates
        dates = self.extract_dates_ml(experience_text)

        # Look for job titles
        job_title_patterns = [
            r'\b(Software Engineer|Developer|Programmer|Analyst|Manager|Lead|Senior|Junior)\b',
            r'\b(Frontend|Backend|Full Stack|Fullstack)\s+(Developer|Engineer)\b',
            r'\b(Angular|React|Python|Java)\s+(Developer|Engineer)\b'
        ]

        job_titles = []
        for pattern in job_title_patterns:
            matches = re.findall(pattern, experience_text, re.IGNORECASE)
            job_titles.extend([match if isinstance(match, str) else ' '.join(match) for match in matches])

        # Create experience entries
        max_entries = max(len(companies), len(job_titles), len(dates) // 2)

        for i in range(min(max_entries, 3)):  # Limit to 3 entries
            experience_entry = {
                "company": companies[i] if i < len(companies) else "",
                "role": job_titles[i] if i < len(job_titles) else "",
                "start_date": dates[i*2] if i*2 < len(dates) else {"year": 0, "month": 0, "day": 0},
                "end_date": dates[i*2+1] if i*2+1 < len(dates) else {"year": 0, "month": 0, "day": 0},
                "description": "",
                "responsibilities": []
            }

            # Extract responsibilities from the experience section
            lines = experience_text.split('\n')
            responsibilities = []
            for line in lines:
                line = line.strip()
                if line.startswith('-') or line.startswith('•') or line.startswith('*'):
                    responsibilities.append(line[1:].strip())

            experience_entry["responsibilities"] = responsibilities[:5]  # Limit to 5 responsibilities
            experience_entries.append(experience_entry)

        return experience_entries

    def extract_address_ml(self, text: str) -> Dict[str, str]:
        """Extract address information using patterns"""
        address = {
            "country": "",
            "state": "",
            "city": "",
            "pincode": ""
        }

        # Extract pincode
        pincode_pattern = r'\b\d{5,6}\b'
        pincode_match = re.search(pincode_pattern, text)
        if pincode_match:
            address["pincode"] = pincode_match.group(0)

        # Extract country
        country_patterns = [
            r'\b(India|USA|United States|UK|United Kingdom|Canada|Australia)\b'
        ]
        for pattern in country_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                address["country"] = match.group(0)
                break

        # Extract cities (Indian cities for now)
        indian_cities = ['Mumbai', 'Delhi', 'Bangalore', 'Hyderabad', 'Chennai', 'Kolkata', 'Pune', 'Ahmedabad']
        for city in indian_cities:
            if re.search(rf'\b{city}\b', text, re.IGNORECASE):
                address["city"] = city
                break

        return address

    def calculate_confidence_scores_ml(self, result: Dict[str, Any]) -> Dict[str, float]:
        """Calculate confidence scores for ML-based extraction"""
        scores = {}

        # Name confidence
        scores["name"] = 1.0 if result["name"] else 0.0

        # Email confidence
        scores["email"] = 1.0 if result["email"] else 0.0

        # Phone confidence
        scores["phone"] = 1.0 if result["phone"] else 0.0

        # Address confidence
        address_fields = [v for v in result["address"].values() if v]
        scores["address"] = len(address_fields) / 4.0

        # Skills confidence
        scores["skills"] = min(1.0, len(result["skills"]) / 10.0)

        # Education confidence
        scores["education"] = min(1.0, len(result["education"]) / 2.0)

        # Experience confidence
        scores["experience"] = min(1.0, len(result["experience"]) / 2.0)

        # Overall confidence
        scores["overall"] = sum(scores.values()) / len(scores)

        return {k: round(v, 2) for k, v in scores.items()}

    def save_model(self, filepath: str):
        """Save trained models to disk"""
        model_data = {
            'section_classifier': self.section_classifier,
            'entity_patterns': self.entity_patterns,
            'skill_keywords': self.skill_keywords
        }

        with open(filepath, 'wb') as f:
            pickle.dump(model_data, f)

        logger.info(f"Model saved to {filepath}")

    def load_model(self, filepath: str):
        """Load trained models from disk"""
        if os.path.exists(filepath):
            with open(filepath, 'rb') as f:
                model_data = pickle.load(f)

            self.section_classifier = model_data['section_classifier']
            self.entity_patterns = model_data['entity_patterns']
            self.skill_keywords = model_data['skill_keywords']

            # Rebuild entity extractors
            self.train_entity_extractors()

            logger.info(f"Model loaded from {filepath}")
        else:
            logger.warning(f"Model file {filepath} not found")

def test_ml_parser():
    """Test the ML parser with sample text"""
    sample_text = """
    John Doe
    Senior Angular Developer
    <EMAIL>
    +91 9876543210
    Mumbai, India 400001

    Work Experience
    Senior Angular Developer at Tech Solutions Pvt Ltd
    June 2021 - March 2023
    - Developed Angular applications using TypeScript
    - Implemented responsive designs with Bootstrap
    - Led a team of 3 developers

    Software Engineer at StartupCorp Inc
    January 2019 - May 2021
    - Built React applications
    - Worked with Node.js backend

    Education
    B.Tech in Computer Science
    IIT Delhi
    2015-2019

    Technical Skills
    Programming Languages: JavaScript, TypeScript, Python, Java
    Frameworks: Angular, React, Node.js, Express
    Databases: MongoDB, MySQL, PostgreSQL
    Cloud: AWS, Docker, Kubernetes
    """

    parser = MLResumeParser()
    result = parser.parse_resume(sample_text)

    print("ML Parser Results:")
    print(json.dumps(result, indent=2))

    # Save the model for future use
    parser.save_model("ml_resume_model.pkl")

    return result

if __name__ == "__main__":
    test_ml_parser()
