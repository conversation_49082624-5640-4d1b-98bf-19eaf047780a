"""
Local LLM Training Approach for Resume Parsing
This is a fallback approach if other methods don't yield satisfactory results.
Uses local LLM fine-tuning for resume entity extraction.
"""

import json
import os
import uuid
from datetime import datetime
from typing import Dict, List, Any, Tuple
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LocalLLMTrainingSetup:
    """
    Setup for training a local LLM for resume parsing
    """
    
    def __init__(self):
        self.training_data = []
        self.model_config = {}
        self.setup_training_framework()
    
    def setup_training_framework(self):
        """Setup the training framework configuration"""
        self.model_config = {
            "approach": "local_llm_fine_tuning",
            "recommended_models": [
                {
                    "name": "microsoft/DialoGPT-medium",
                    "size": "345M parameters",
                    "memory_requirement": "2-4GB GPU",
                    "training_time": "2-4 hours",
                    "use_case": "Conversational entity extraction"
                },
                {
                    "name": "distilbert-base-uncased",
                    "size": "66M parameters", 
                    "memory_requirement": "1-2GB GPU",
                    "training_time": "1-2 hours",
                    "use_case": "Token classification for NER"
                },
                {
                    "name": "microsoft/DialoGPT-small",
                    "size": "117M parameters",
                    "memory_requirement": "1-2GB GPU", 
                    "training_time": "1-3 hours",
                    "use_case": "Lightweight entity extraction"
                }
            ],
            "training_framework": "transformers + pytorch",
            "hardware_requirements": {
                "minimum": "8GB RAM, CPU only (slow)",
                "recommended": "16GB RAM + 4GB GPU",
                "optimal": "32GB RAM + 8GB+ GPU"
            }
        }
    
    def generate_training_data(self) -> List[Dict[str, Any]]:
        """
        Generate comprehensive training data for resume parsing
        """
        logger.info("Generating training data for local LLM...")
        
        # Training examples with input-output pairs
        training_examples = [
            {
                "input": """John Doe
                Senior Angular Developer
                <EMAIL>
                +91 9876543210
                Mumbai, India
                
                Experience:
                Senior Angular Developer at Tech Solutions Pvt Ltd
                June 2021 - March 2023
                - Developed Angular applications
                
                Education:
                B.Tech Computer Science, IIT Delhi, 2020
                
                Skills: Angular, TypeScript, JavaScript""",
                
                "output": {
                    "name": "John Doe",
                    "email": "<EMAIL>", 
                    "phone": "+91 9876543210",
                    "address": {"city": "Mumbai", "country": "India"},
                    "skills": ["Angular", "TypeScript", "JavaScript"],
                    "experience": [{
                        "role": "Senior Angular Developer",
                        "company": "Tech Solutions Pvt Ltd",
                        "start_date": {"year": 2021, "month": 6},
                        "end_date": {"year": 2023, "month": 3}
                    }],
                    "education": ["B.Tech Computer Science, IIT Delhi, 2020"]
                }
            },
            
            {
                "input": """Jane Smith
                Frontend Developer
                <EMAIL>
                (*************
                
                Work Experience:
                Frontend Developer at WebCorp
                2020-2023
                - Built React applications
                - Worked with REST APIs
                
                Education:
                Master of Computer Science
                Stanford University
                2018-2020
                
                Technical Skills:
                React, JavaScript, HTML, CSS, Node.js""",
                
                "output": {
                    "name": "Jane Smith",
                    "email": "<EMAIL>",
                    "phone": "(*************", 
                    "skills": ["React", "JavaScript", "HTML", "CSS", "Node.js"],
                    "experience": [{
                        "role": "Frontend Developer",
                        "company": "WebCorp",
                        "start_date": {"year": 2020},
                        "end_date": {"year": 2023},
                        "responsibilities": ["Built React applications", "Worked with REST APIs"]
                    }],
                    "education": ["Master of Computer Science, Stanford University, 2018-2020"]
                }
            },
            
            # Add more training examples...
            {
                "input": """Michael Johnson
                Full Stack Developer
                <EMAIL>
                ******-567-8900
                San Francisco, CA
                
                Professional Experience:
                Senior Full Stack Developer
                Google Inc
                2021 - Present
                - Lead development of microservices
                - Mentored junior developers
                
                Full Stack Developer  
                Facebook Inc
                2019 - 2021
                - Developed React applications
                - Built Node.js APIs
                
                Education:
                MS Computer Science, Stanford University, 2017-2019
                BE Software Engineering, UC Berkeley, 2013-2017
                
                Skills:
                Python, JavaScript, React, Node.js, PostgreSQL, AWS""",
                
                "output": {
                    "name": "Michael Johnson",
                    "email": "<EMAIL>",
                    "phone": "******-567-8900",
                    "address": {"city": "San Francisco", "state": "CA"},
                    "skills": ["Python", "JavaScript", "React", "Node.js", "PostgreSQL", "AWS"],
                    "experience": [
                        {
                            "role": "Senior Full Stack Developer",
                            "company": "Google Inc", 
                            "start_date": {"year": 2021},
                            "end_date": {"year": 2024, "month": 1},
                            "responsibilities": ["Lead development of microservices", "Mentored junior developers"]
                        },
                        {
                            "role": "Full Stack Developer",
                            "company": "Facebook Inc",
                            "start_date": {"year": 2019}, 
                            "end_date": {"year": 2021},
                            "responsibilities": ["Developed React applications", "Built Node.js APIs"]
                        }
                    ],
                    "education": [
                        "MS Computer Science, Stanford University, 2017-2019",
                        "BE Software Engineering, UC Berkeley, 2013-2017"
                    ]
                }
            }
        ]
        
        # Generate variations and augmented data
        augmented_data = self.augment_training_data(training_examples)
        
        self.training_data = training_examples + augmented_data
        logger.info(f"Generated {len(self.training_data)} training examples")
        
        return self.training_data
    
    def augment_training_data(self, base_examples: List[Dict]) -> List[Dict]:
        """
        Create variations of training data to improve model robustness
        """
        augmented = []
        
        # Create variations with different formatting
        for example in base_examples:
            # Variation 1: Different section headers
            modified_input = example["input"].replace("Experience:", "Work Experience:")
            modified_input = modified_input.replace("Skills:", "Technical Skills:")
            
            augmented.append({
                "input": modified_input,
                "output": example["output"]
            })
            
            # Variation 2: Different phone format
            if "+91" in example["input"]:
                modified_input = example["input"].replace("+91 9876543210", "9876543210")
                modified_output = example["output"].copy()
                modified_output["phone"] = "9876543210"
                
                augmented.append({
                    "input": modified_input,
                    "output": modified_output
                })
        
        return augmented
    
    def create_training_script(self) -> str:
        """
        Generate a complete training script for local LLM fine-tuning
        """
        script = '''
"""
Local LLM Fine-tuning Script for Resume Parsing
Run this script to train a local model for resume entity extraction
"""

import torch
from transformers import (
    AutoTokenizer, AutoModelForCausalLM, 
    TrainingArguments, Trainer, DataCollatorForLanguageModeling
)
from datasets import Dataset
import json
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ResumeParsingTrainer:
    def __init__(self, model_name="microsoft/DialoGPT-small"):
        self.model_name = model_name
        self.tokenizer = AutoTokenizer.from_pretrained(model_name)
        self.model = AutoModelForCausalLM.from_pretrained(model_name)
        
        # Add padding token if not present
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
    
    def prepare_training_data(self, training_examples):
        """Convert training examples to model input format"""
        formatted_data = []
        
        for example in training_examples:
            # Create prompt-response format
            prompt = f"Extract structured data from this resume:\\n{example['input']}\\n\\nStructured Output:"
            response = json.dumps(example['output'])
            
            # Combine prompt and response
            full_text = f"{prompt} {response}{self.tokenizer.eos_token}"
            formatted_data.append({"text": full_text})
        
        return formatted_data
    
    def tokenize_data(self, examples):
        """Tokenize the training data"""
        return self.tokenizer(
            examples["text"],
            truncation=True,
            padding=True,
            max_length=512,
            return_tensors="pt"
        )
    
    def train_model(self, training_data, output_dir="./resume_parser_model"):
        """Train the model on resume parsing data"""
        logger.info("Starting model training...")
        
        # Prepare data
        formatted_data = self.prepare_training_data(training_data)
        dataset = Dataset.from_list(formatted_data)
        tokenized_dataset = dataset.map(self.tokenize_data, batched=True)
        
        # Training arguments
        training_args = TrainingArguments(
            output_dir=output_dir,
            overwrite_output_dir=True,
            num_train_epochs=3,
            per_device_train_batch_size=2,
            save_steps=500,
            save_total_limit=2,
            prediction_loss_only=True,
            logging_steps=100,
            learning_rate=5e-5,
            warmup_steps=100,
        )
        
        # Data collator
        data_collator = DataCollatorForLanguageModeling(
            tokenizer=self.tokenizer,
            mlm=False,
        )
        
        # Trainer
        trainer = Trainer(
            model=self.model,
            args=training_args,
            data_collator=data_collator,
            train_dataset=tokenized_dataset,
        )
        
        # Train
        trainer.train()
        
        # Save model
        trainer.save_model()
        self.tokenizer.save_pretrained(output_dir)
        
        logger.info(f"Model training completed. Saved to {output_dir}")
    
    def generate_response(self, resume_text, max_length=512):
        """Generate structured output for a resume"""
        prompt = f"Extract structured data from this resume:\\n{resume_text}\\n\\nStructured Output:"
        
        inputs = self.tokenizer.encode(prompt, return_tensors="pt")
        
        with torch.no_grad():
            outputs = self.model.generate(
                inputs,
                max_length=max_length,
                num_return_sequences=1,
                temperature=0.7,
                pad_token_id=self.tokenizer.eos_token_id
            )
        
        response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
        
        # Extract just the generated part
        generated_part = response[len(prompt):].strip()
        
        try:
            # Try to parse as JSON
            return json.loads(generated_part)
        except json.JSONDecodeError:
            return {"raw_output": generated_part, "error": "Could not parse as JSON"}

# Usage example
if __name__ == "__main__":
    # Load training data
    with open("training_data.json", "r") as f:
        training_data = json.load(f)
    
    # Initialize trainer
    trainer = ResumeParsingTrainer()
    
    # Train model
    trainer.train_model(training_data)
    
    # Test the trained model
    test_resume = """
    Alice Brown
    Software Engineer
    <EMAIL>
    +1-555-0123
    
    Experience:
    Software Engineer at TechCorp
    2022-Present
    - Developed web applications
    
    Skills: Python, Django, PostgreSQL
    """
    
    result = trainer.generate_response(test_resume)
    print("Generated output:", json.dumps(result, indent=2))
'''
        
        return script
    
    def save_training_setup(self, output_dir: str = "llm_training_setup"):
        """Save all training materials to disk"""
        os.makedirs(output_dir, exist_ok=True)
        
        # Save training data
        training_data = self.generate_training_data()
        with open(os.path.join(output_dir, "training_data.json"), "w") as f:
            json.dump(training_data, f, indent=2)
        
        # Save training script
        script = self.create_training_script()
        with open(os.path.join(output_dir, "train_model.py"), "w") as f:
            f.write(script)
        
        # Save configuration
        with open(os.path.join(output_dir, "config.json"), "w") as f:
            json.dump(self.model_config, f, indent=2)
        
        # Save requirements
        requirements = """
# Local LLM Training Requirements
torch>=1.9.0
transformers>=4.20.0
datasets>=2.0.0
accelerate>=0.20.0
tensorboard>=2.9.0

# Optional: For GPU acceleration
# torch-audio  # if using audio features
# torch-vision # if using vision features
"""
        with open(os.path.join(output_dir, "requirements_llm.txt"), "w") as f:
            f.write(requirements)
        
        # Save instructions
        instructions = """
# Local LLM Training Instructions

## Prerequisites
1. Install Python 3.8+
2. Install PyTorch: https://pytorch.org/get-started/locally/
3. Install requirements: pip install -r requirements_llm.txt

## Training Steps
1. Prepare your data: python -c "from local_llm_training import LocalLLMTrainingSetup; setup = LocalLLMTrainingSetup(); setup.save_training_setup()"
2. Run training: python train_model.py
3. Wait for training to complete (1-4 hours depending on hardware)
4. Test the model with your resume data

## Hardware Requirements
- Minimum: 8GB RAM, CPU only (very slow)
- Recommended: 16GB RAM + 4GB GPU
- Optimal: 32GB RAM + 8GB+ GPU

## Model Options
- DialoGPT-small: Fastest training, good for testing
- DialoGPT-medium: Better quality, longer training
- DistilBERT: Good for token classification tasks

## Troubleshooting
- Out of memory: Reduce batch_size in training_args
- Slow training: Use GPU if available
- Poor results: Add more training data, increase epochs
"""
        
        with open(os.path.join(output_dir, "TRAINING_INSTRUCTIONS.md"), "w") as f:
            f.write(instructions)
        
        logger.info(f"Training setup saved to {output_dir}/")
        logger.info("Follow TRAINING_INSTRUCTIONS.md to train your local model")
        
        return output_dir

def main():
    """Main function to setup local LLM training"""
    print("🤖 Local LLM Training Setup for Resume Parsing")
    print("=" * 50)
    
    setup = LocalLLMTrainingSetup()
    output_dir = setup.save_training_setup()
    
    print(f"✅ Training setup complete!")
    print(f"📁 Files saved to: {output_dir}/")
    print(f"📖 Read {output_dir}/TRAINING_INSTRUCTIONS.md for next steps")
    print("\n🚀 Quick start:")
    print(f"   cd {output_dir}")
    print("   pip install -r requirements_llm.txt")
    print("   python train_model.py")

if __name__ == "__main__":
    main()
