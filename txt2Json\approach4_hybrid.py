"""
Approach 4: Hybrid Resume Parsing
Combines rule-based, NLP, and ML approaches for maximum accuracy
"""

import json
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional
import logging
import statistics

# Import our custom parsers
try:
    from .apporach1 import RuleBasedResumeParser
    from .approach2 import SpacyResumeParser
    from .approach3 import MLResumeParser
except ImportError:
    # Handle relative imports when running as script
    import sys
    import os
    sys.path.append(os.path.dirname(__file__))
    from apporach1 import RuleBasedResumeParser
    from approach2 import SpacyResumeParser
    from approach3 import MLResumeParser

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class HybridResumeParser:
    """
    Hybrid resume parser that combines multiple approaches for best results
    """
    
    def __init__(self):
        """Initialize all parsers"""
        logger.info("Initializing hybrid parser...")
        
        # Initialize individual parsers
        self.rule_parser = RuleBasedResumeParser()
        
        try:
            self.spacy_parser = SpacyResumeParser()
            self.spacy_available = True
        except Exception as e:
            logger.warning(f"spaCy parser not available: {e}")
            self.spacy_parser = None
            self.spacy_available = False
        
        self.ml_parser = MLResumeParser()
        
        # Weights for combining results (can be tuned based on performance)
        self.weights = {
            'rule_based': 0.4,
            'spacy': 0.3,
            'ml': 0.3
        }
        
        if not self.spacy_available:
            # Redistribute weights if spaCy is not available
            self.weights = {
                'rule_based': 0.6,
                'spacy': 0.0,
                'ml': 0.4
            }
    
    def parse_resume(self, text: str) -> Dict[str, Any]:
        """
        Parse resume using hybrid approach
        
        Args:
            text: Raw resume text
            
        Returns:
            Combined structured JSON data
        """
        logger.info("Starting hybrid resume parsing...")
        
        # Get results from all available parsers
        results = {}
        
        # Rule-based parsing
        try:
            results['rule_based'] = self.rule_parser.parse_resume(text)
            logger.info("Rule-based parsing completed")
        except Exception as e:
            logger.error(f"Rule-based parsing failed: {e}")
            results['rule_based'] = None
        
        # spaCy parsing
        if self.spacy_available:
            try:
                results['spacy'] = self.spacy_parser.parse_resume(text)
                logger.info("spaCy parsing completed")
            except Exception as e:
                logger.error(f"spaCy parsing failed: {e}")
                results['spacy'] = None
        else:
            results['spacy'] = None
        
        # ML parsing
        try:
            results['ml'] = self.ml_parser.parse_resume(text)
            logger.info("ML parsing completed")
        except Exception as e:
            logger.error(f"ML parsing failed: {e}")
            results['ml'] = None
        
        # Combine results
        combined_result = self.combine_results(results)
        
        logger.info("Hybrid parsing completed")
        return combined_result
    
    def combine_results(self, results: Dict[str, Optional[Dict]]) -> Dict[str, Any]:
        """
        Combine results from different parsers using weighted voting and confidence scores
        """
        # Initialize combined result
        combined = {
            "resume_id": str(uuid.uuid4()),
            "address": {
                "country": "",
                "state": "",
                "city": "",
                "pincode": ""
            },
            "name": "",
            "email": "",
            "phone": "",
            "skills": [],
            "education": [],
            "experience": [],
            "parsing_metadata": {
                "method": "hybrid",
                "timestamp": datetime.now().isoformat(),
                "confidence_scores": {},
                "individual_results": {},
                "combination_strategy": "weighted_voting"
            }
        }
        
        # Store individual results for debugging
        combined["parsing_metadata"]["individual_results"] = {
            k: v["parsing_metadata"]["confidence_scores"] if v else None 
            for k, v in results.items()
        }
        
        # Combine each field
        combined["name"] = self.combine_names(results)
        combined["email"] = self.combine_emails(results)
        combined["phone"] = self.combine_phones(results)
        combined["address"] = self.combine_addresses(results)
        combined["skills"] = self.combine_skills(results)
        combined["education"] = self.combine_education(results)
        combined["experience"] = self.combine_experience(results)
        
        # Calculate combined confidence scores
        combined["parsing_metadata"]["confidence_scores"] = self.calculate_combined_confidence(results, combined)
        
        return combined
    
    def combine_names(self, results: Dict[str, Optional[Dict]]) -> str:
        """Combine name extraction results"""
        names = []
        confidences = []
        
        for parser_name, result in results.items():
            if result and result.get("name"):
                names.append(result["name"])
                # Use confidence score if available
                conf = result.get("parsing_metadata", {}).get("confidence_scores", {}).get("name", 0.5)
                confidences.append(conf * self.weights.get(parser_name, 0))
        
        if names:
            # Return the name with highest weighted confidence
            best_idx = confidences.index(max(confidences))
            return names[best_idx]
        
        return ""
    
    def combine_emails(self, results: Dict[str, Optional[Dict]]) -> str:
        """Combine email extraction results"""
        emails = []
        
        for result in results.values():
            if result and result.get("email"):
                emails.append(result["email"])
        
        if emails:
            # Return the first valid email (they should all be the same if correct)
            return emails[0]
        
        return ""
    
    def combine_phones(self, results: Dict[str, Optional[Dict]]) -> str:
        """Combine phone extraction results"""
        phones = []
        
        for result in results.values():
            if result and result.get("phone"):
                phones.append(result["phone"])
        
        if phones:
            # Return the first valid phone
            return phones[0]
        
        return ""
    
    def combine_addresses(self, results: Dict[str, Optional[Dict]]) -> Dict[str, str]:
        """Combine address extraction results"""
        combined_address = {
            "country": "",
            "state": "",
            "city": "",
            "pincode": ""
        }
        
        # Collect all address components
        for result in results.values():
            if result and result.get("address"):
                addr = result["address"]
                for field in combined_address.keys():
                    if addr.get(field) and not combined_address[field]:
                        combined_address[field] = addr[field]
        
        return combined_address
    
    def combine_skills(self, results: Dict[str, Optional[Dict]]) -> List[str]:
        """Combine skills extraction results"""
        all_skills = set()
        
        for result in results.values():
            if result and result.get("skills"):
                all_skills.update(result["skills"])
        
        return sorted(list(all_skills))
    
    def combine_education(self, results: Dict[str, Optional[Dict]]) -> List[str]:
        """Combine education extraction results"""
        all_education = set()
        
        for result in results.values():
            if result and result.get("education"):
                all_education.update(result["education"])
        
        return list(all_education)[:5]  # Limit to 5 entries
    
    def combine_experience(self, results: Dict[str, Optional[Dict]]) -> List[Dict[str, Any]]:
        """Combine experience extraction results"""
        # This is more complex as we need to merge similar experiences
        all_experiences = []
        
        for result in results.values():
            if result and result.get("experience"):
                all_experiences.extend(result["experience"])
        
        # Simple deduplication based on company and role
        unique_experiences = []
        seen = set()
        
        for exp in all_experiences:
            key = (exp.get("company", "").lower(), exp.get("role", "").lower())
            if key not in seen and (exp.get("company") or exp.get("role")):
                seen.add(key)
                unique_experiences.append(exp)
        
        return unique_experiences[:5]  # Limit to 5 entries
    
    def calculate_combined_confidence(self, results: Dict[str, Optional[Dict]], combined: Dict[str, Any]) -> Dict[str, float]:
        """Calculate confidence scores for the combined result"""
        scores = {}
        
        # For each field, calculate weighted average of individual confidences
        fields = ["name", "email", "phone", "address", "skills", "education", "experience"]
        
        for field in fields:
            field_scores = []
            weights = []
            
            for parser_name, result in results.items():
                if result and result.get("parsing_metadata", {}).get("confidence_scores", {}).get(field) is not None:
                    conf = result["parsing_metadata"]["confidence_scores"][field]
                    field_scores.append(conf)
                    weights.append(self.weights.get(parser_name, 0))
            
            if field_scores:
                # Weighted average
                weighted_score = sum(s * w for s, w in zip(field_scores, weights)) / sum(weights)
                scores[field] = round(weighted_score, 2)
            else:
                scores[field] = 0.0
        
        # Overall confidence
        scores["overall"] = round(statistics.mean(scores.values()), 2)
        
        return scores

def test_hybrid_parser():
    """Test the hybrid parser with sample text"""
    sample_text = """
    John Doe
    Senior Angular Developer
    <EMAIL>
    +91 9876543210
    Mumbai, India 400001
    
    Professional Experience
    Senior Angular Developer at Tech Solutions Pvt Ltd
    June 2021 - March 2023
    - Developed Angular applications using TypeScript
    - Implemented responsive designs with Bootstrap
    - Led a team of 3 developers
    
    Software Engineer at StartupCorp Inc
    January 2019 - May 2021
    - Built React applications
    - Worked with Node.js backend
    
    Education
    B.Tech in Computer Science
    Indian Institute of Technology Delhi
    2015-2019
    
    Technical Skills
    Programming Languages: JavaScript, TypeScript, Python, Java
    Frontend Frameworks: Angular, React, Vue.js
    Backend Technologies: Node.js, Express, Django
    Databases: MongoDB, MySQL, PostgreSQL
    Cloud Platforms: AWS, Azure, Docker, Kubernetes
    Tools: Git, Jenkins, Jira, Postman
    """
    
    parser = HybridResumeParser()
    result = parser.parse_resume(sample_text)
    
    print("Hybrid Parser Results:")
    print(json.dumps(result, indent=2))
    
    return result

if __name__ == "__main__":
    test_hybrid_parser()
