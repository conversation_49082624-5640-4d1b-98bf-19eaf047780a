"""
Approach 2: NLP-based Resume Parsing using spaCy
Uses open-source spaCy models for named entity recognition and pattern matching
"""

import spacy
import re
import json
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional
import logging
from collections import defaultdict

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SpacyResumeParser:
    """
    NLP-based resume parser using spaCy for entity recognition
    """

    def __init__(self, model_name: str = "en_core_web_sm"):
        """
        Initialize spaCy parser

        Args:
            model_name: spaCy model to use (en_core_web_sm is free)
        """
        try:
            self.nlp = spacy.load(model_name)
            logger.info(f"Loaded spaCy model: {model_name}")
        except OSError:
            logger.error(f"spaCy model '{model_name}' not found. Install with: python -m spacy download {model_name}")
            raise

        self.setup_custom_patterns()
        self.setup_skill_database()

    def setup_custom_patterns(self):
        """Setup custom patterns for resume-specific entities"""

        # Add custom patterns to the matcher
        from spacy.matcher import Matcher
        self.matcher = Matcher(self.nlp.vocab)

        # Email patterns
        email_pattern = [{"LIKE_EMAIL": True}]
        self.matcher.add("EMAIL", [email_pattern])

        # Phone patterns
        phone_patterns = [
            [{"TEXT": {"REGEX": r"\+?\d{1,3}"}}, {"TEXT": {"REGEX": r"\d{10}"}}],
            [{"TEXT": {"REGEX": r"\(\d{3}\)"}}, {"TEXT": {"REGEX": r"\d{3}-\d{4}"}}],
            [{"TEXT": {"REGEX": r"\d{3}-\d{3}-\d{4}"}}],
            [{"TEXT": {"REGEX": r"\d{10}"}}]
        ]
        for i, pattern in enumerate(phone_patterns):
            self.matcher.add(f"PHONE_{i}", [pattern])

        # Education patterns
        education_patterns = [
            [{"LOWER": {"IN": ["bachelor", "b.tech", "b.e", "b.sc", "bachelor's"]}},
             {"LOWER": {"IN": ["of", "in"]}, "OP": "?"},
             {"POS": "NOUN", "OP": "+"}],
            [{"LOWER": {"IN": ["master", "m.tech", "m.e", "m.sc", "master's"]}},
             {"LOWER": {"IN": ["of", "in"]}, "OP": "?"},
             {"POS": "NOUN", "OP": "+"}],
            [{"LOWER": {"IN": ["phd", "doctorate", "ph.d"]}},
             {"LOWER": {"IN": ["in"]}, "OP": "?"},
             {"POS": "NOUN", "OP": "*"}]
        ]
        for i, pattern in enumerate(education_patterns):
            self.matcher.add(f"EDUCATION_{i}", [pattern])

        # Job title patterns
        job_patterns = [
            [{"LOWER": {"IN": ["software", "senior", "junior", "lead"]}},
             {"LOWER": {"IN": ["engineer", "developer", "programmer", "analyst"]}}],
            [{"LOWER": {"IN": ["frontend", "backend", "full", "fullstack"]}},
             {"LOWER": "developer"}],
            [{"LOWER": "angular"}, {"LOWER": "developer"}],
            [{"LOWER": {"IN": ["project", "product", "technical"]}},
             {"LOWER": "manager"}]
        ]
        for i, pattern in enumerate(job_patterns):
            self.matcher.add(f"JOB_TITLE_{i}", [pattern])

        # Company patterns
        company_patterns = [
            [{"POS": "PROPN", "OP": "+"},
             {"LOWER": {"IN": ["ltd", "inc", "corp", "corporation", "company", "technologies", "solutions", "systems"]}}],
            [{"POS": "PROPN", "OP": "+"},
             {"LOWER": {"IN": ["pvt", "private"]}},
             {"LOWER": {"IN": ["ltd", "limited"]}}]
        ]
        for i, pattern in enumerate(company_patterns):
            self.matcher.add(f"COMPANY_{i}", [pattern])

    def setup_skill_database(self):
        """Setup comprehensive skill database"""
        self.skill_categories = {
            'programming_languages': [
                'javascript', 'typescript', 'python', 'java', 'c#', 'c++', 'c', 'php', 'ruby', 'go', 'rust', 'swift', 'kotlin'
            ],
            'frontend_frameworks': [
                'angular', 'react', 'vue', 'svelte', 'ember', 'backbone', 'jquery'
            ],
            'backend_frameworks': [
                'node.js', 'express', 'django', 'flask', 'spring', 'asp.net', 'laravel', 'rails', 'fastapi'
            ],
            'databases': [
                'mysql', 'postgresql', 'mongodb', 'redis', 'sqlite', 'oracle', 'sql server', 'cassandra', 'dynamodb'
            ],
            'cloud_platforms': [
                'aws', 'azure', 'gcp', 'google cloud', 'heroku', 'digitalocean', 'linode'
            ],
            'devops_tools': [
                'docker', 'kubernetes', 'jenkins', 'gitlab ci', 'github actions', 'terraform', 'ansible', 'chef', 'puppet'
            ],
            'web_technologies': [
                'html', 'css', 'scss', 'sass', 'less', 'bootstrap', 'tailwind', 'material-ui', 'webpack', 'vite'
            ],
            'testing_tools': [
                'jest', 'mocha', 'jasmine', 'cypress', 'selenium', 'pytest', 'junit', 'testng'
            ],
            'version_control': [
                'git', 'github', 'gitlab', 'bitbucket', 'svn'
            ],
            'project_management': [
                'jira', 'confluence', 'trello', 'asana', 'slack', 'teams'
            ]
        }

        # Create flat list for matching
        self.all_skills = []
        for category_skills in self.skill_categories.values():
            self.all_skills.extend(category_skills)

        # Create skill patterns for spaCy matcher
        skill_patterns = []
        for skill in self.all_skills:
            if ' ' in skill:  # Multi-word skills
                words = skill.split()
                pattern = [{"LOWER": word.lower()} for word in words]
                skill_patterns.append(pattern)
            else:  # Single word skills
                pattern = [{"LOWER": skill.lower()}]
                skill_patterns.append(pattern)

        # Add skill patterns to matcher
        for i, pattern in enumerate(skill_patterns):
            self.matcher.add(f"SKILL_{i}", [pattern])

    def parse_resume(self, text: str) -> Dict[str, Any]:
        """
        Main parsing function using spaCy NLP

        Args:
            text: Raw resume text

        Returns:
            Structured JSON data
        """
        logger.info("Starting spaCy-based resume parsing...")

        # Process text with spaCy
        doc = self.nlp(text)

        # Initialize result structure
        result = {
            "resume_id": str(uuid.uuid4()),
            "address": {
                "country": "",
                "state": "",
                "city": "",
                "pincode": ""
            },
            "name": "",
            "email": "",
            "phone": "",
            "skills": [],
            "education": [],
            "experience": [],
            "parsing_metadata": {
                "method": "spacy_nlp",
                "timestamp": datetime.now().isoformat(),
                "confidence_scores": {},
                "entities_found": []
            }
        }

        # Extract entities using spaCy NER
        result["name"] = self.extract_name_nlp(doc)
        result["email"] = self.extract_email_nlp(doc)
        result["phone"] = self.extract_phone_nlp(doc)
        result["address"] = self.extract_address_nlp(doc)
        result["skills"] = self.extract_skills_nlp(doc)
        result["education"] = self.extract_education_nlp(doc)
        result["experience"] = self.extract_experience_nlp(doc, text)

        # Store found entities for debugging
        result["parsing_metadata"]["entities_found"] = [
            {"text": ent.text, "label": ent.label_, "start": ent.start_char, "end": ent.end_char}
            for ent in doc.ents
        ]

        # Calculate confidence scores
        result["parsing_metadata"]["confidence_scores"] = self.calculate_confidence_scores(result, doc)

        logger.info(f"spaCy parsing completed. Found {len(doc.ents)} entities, extracted {len(result['skills'])} skills")

        return result

    def extract_name_nlp(self, doc) -> str:
        """Extract name using spaCy NER"""
        # Look for PERSON entities
        for ent in doc.ents:
            if ent.label_ == "PERSON":
                # Take the first person entity found
                return ent.text.title()

        # Fallback: look at the beginning of the document
        sentences = list(doc.sents)
        if sentences:
            first_sentence = sentences[0]
            for token in first_sentence:
                if token.pos_ == "PROPN" and not token.like_email and not token.like_num:
                    # Try to get full name by looking at consecutive proper nouns
                    name_tokens = [token]
                    for next_token in first_sentence[token.i+1:]:
                        if next_token.pos_ == "PROPN":
                            name_tokens.append(next_token)
                        else:
                            break
                    if len(name_tokens) >= 2:
                        return " ".join([t.text for t in name_tokens]).title()

        return ""

    def extract_email_nlp(self, doc) -> str:
        """Extract email using spaCy patterns"""
        matches = self.matcher(doc)
        for match_id, start, end in matches:
            label = self.nlp.vocab.strings[match_id]
            if label == "EMAIL":
                return doc[start:end].text

        # Fallback: look for email-like tokens
        for token in doc:
            if token.like_email:
                return token.text

        return ""

    def extract_phone_nlp(self, doc) -> str:
        """Extract phone using spaCy patterns"""
        matches = self.matcher(doc)
        for match_id, start, end in matches:
            label = self.nlp.vocab.strings[match_id]
            if label.startswith("PHONE"):
                return doc[start:end].text

        return ""

    def extract_address_nlp(self, doc) -> Dict[str, str]:
        """Extract address using spaCy NER"""
        address = {
            "country": "",
            "state": "",
            "city": "",
            "pincode": ""
        }

        # Look for location entities
        for ent in doc.ents:
            if ent.label_ in ["GPE", "LOC"]:  # Geopolitical entity or location
                text = ent.text.lower()

                # Check for countries
                countries = ["india", "usa", "united states", "uk", "united kingdom", "canada", "australia"]
                if any(country in text for country in countries):
                    address["country"] = ent.text

                # Check for Indian cities
                indian_cities = ["mumbai", "delhi", "bangalore", "hyderabad", "chennai", "kolkata", "pune"]
                if any(city in text for city in indian_cities):
                    address["city"] = ent.text

        # Look for pincode (numbers)
        for token in doc:
            if token.like_num and len(token.text) in [5, 6]:
                address["pincode"] = token.text
                break

        return address

    def extract_skills_nlp(self, doc) -> List[str]:
        """Extract skills using spaCy patterns and NER"""
        found_skills = set()

        # Use custom patterns
        matches = self.matcher(doc)
        for match_id, start, end in matches:
            label = self.nlp.vocab.strings[match_id]
            if label.startswith("SKILL"):
                skill_text = doc[start:end].text.lower()
                # Find which skill this matches
                for skill in self.all_skills:
                    if skill.lower() == skill_text:
                        found_skills.add(skill.title())
                        break

        # Also look for skills in noun chunks
        for chunk in doc.noun_chunks:
            chunk_text = chunk.text.lower()
            for skill in self.all_skills:
                if skill.lower() in chunk_text:
                    found_skills.add(skill.title())

        return sorted(list(found_skills))

    def extract_education_nlp(self, doc) -> List[str]:
        """Extract education using spaCy patterns and NER"""
        education_entries = []

        # Use custom education patterns
        matches = self.matcher(doc)
        education_matches = []

        for match_id, start, end in matches:
            label = self.nlp.vocab.strings[match_id]
            if label.startswith("EDUCATION"):
                education_matches.append((start, end))

        # Extract context around education matches
        for start, end in education_matches:
            # Get the sentence containing this match
            span = doc[start:end]
            sentence = span.sent

            # Clean and add the education entry
            education_text = sentence.text.strip()
            if len(education_text) > 10 and education_text not in education_entries:
                education_entries.append(education_text)

        # Also look for organization entities that might be universities
        for ent in doc.ents:
            if ent.label_ == "ORG":
                text = ent.text.lower()
                if any(keyword in text for keyword in ["university", "college", "institute", "school"]):
                    # Get the sentence containing this organization
                    sentence = ent.sent.text.strip()
                    if len(sentence) > 10 and sentence not in education_entries:
                        education_entries.append(sentence)

        return education_entries[:5]  # Limit to 5 entries

    def extract_experience_nlp(self, doc, original_text: str) -> List[Dict[str, Any]]:
        """Extract work experience using spaCy patterns and NER"""
        experience_entries = []

        # Find job title matches
        matches = self.matcher(doc)
        job_matches = []
        company_matches = []

        for match_id, start, end in matches:
            label = self.nlp.vocab.strings[match_id]
            if label.startswith("JOB_TITLE"):
                job_matches.append((start, end, doc[start:end].text))
            elif label.startswith("COMPANY"):
                company_matches.append((start, end, doc[start:end].text))

        # Process job matches
        for start, end, job_title in job_matches:
            experience_entry = {
                "company": "",
                "role": job_title,
                "start_date": {"year": 0, "month": 0, "day": 0},
                "end_date": {"year": 0, "month": 0, "day": 0},
                "description": "",
                "responsibilities": []
            }

            # Look for company near this job title
            job_span = doc[start:end]
            sentence = job_span.sent

            # Check for organizations in the same sentence or nearby
            for ent in sentence.ents:
                if ent.label_ == "ORG":
                    experience_entry["company"] = ent.text
                    break

            # If no company found in same sentence, look in nearby sentences
            if not experience_entry["company"]:
                for company_start, company_end, company_text in company_matches:
                    # Check if company is within reasonable distance
                    if abs(company_start - start) < 50:  # Within 50 tokens
                        experience_entry["company"] = company_text
                        break

            # Extract dates from the sentence
            dates = self.extract_dates_from_sentence(sentence)
            if dates:
                if len(dates) >= 2:
                    experience_entry["start_date"] = dates[0]
                    experience_entry["end_date"] = dates[1]
                elif len(dates) == 1:
                    experience_entry["start_date"] = dates[0]

            experience_entries.append(experience_entry)

        return experience_entries[:5]  # Limit to 5 entries

    def extract_dates_from_sentence(self, sentence) -> List[Dict[str, int]]:
        """Extract dates from a spaCy sentence"""
        dates = []

        # Look for DATE entities
        for ent in sentence.ents:
            if ent.label_ == "DATE":
                date_dict = self.parse_date_entity(ent.text)
                if date_dict:
                    dates.append(date_dict)

        # Also look for year patterns
        for token in sentence:
            if token.like_num and len(token.text) == 4:
                try:
                    year = int(token.text)
                    if 1990 <= year <= 2030:
                        dates.append({"year": year, "month": 1, "day": 1})
                except ValueError:
                    continue

        return dates

    def parse_date_entity(self, date_text: str) -> Optional[Dict[str, int]]:
        """Parse a date entity text into structured format"""
        import re

        # Month mapping
        month_map = {
            'jan': 1, 'january': 1, 'feb': 2, 'february': 2, 'mar': 3, 'march': 3,
            'apr': 4, 'april': 4, 'may': 5, 'jun': 6, 'june': 6,
            'jul': 7, 'july': 7, 'aug': 8, 'august': 8, 'sep': 9, 'september': 9,
            'oct': 10, 'october': 10, 'nov': 11, 'november': 11, 'dec': 12, 'december': 12
        }

        date_text = date_text.lower().strip()

        # Try different date patterns
        patterns = [
            r'(\d{1,2})[/-](\d{1,2})[/-](\d{4})',  # DD/MM/YYYY
            r'(\d{4})[/-](\d{1,2})[/-](\d{1,2})',  # YYYY/MM/DD
            r'(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)[a-z]*\s+(\d{4})',  # Month Year
            r'(\d{1,2})\s+(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)[a-z]*\s+(\d{4})',  # DD Month Year
            r'(\d{4})'  # Just year
        ]

        for pattern in patterns:
            match = re.search(pattern, date_text)
            if match:
                groups = match.groups()
                try:
                    if len(groups) == 3:
                        if groups[2].isdigit() and len(groups[2]) == 4:  # Year is last
                            year = int(groups[2])
                            if groups[1].isdigit():
                                month = int(groups[1])
                                day = int(groups[0])
                            else:
                                month = month_map.get(groups[1][:3], 1)
                                day = int(groups[0]) if groups[0].isdigit() else 1
                        else:  # Year is first
                            year = int(groups[0])
                            month = int(groups[1]) if groups[1].isdigit() else month_map.get(groups[1][:3], 1)
                            day = int(groups[2]) if groups[2].isdigit() else 1

                        return {"year": year, "month": month, "day": day}

                    elif len(groups) == 2:
                        if groups[1].isdigit():  # Month Year
                            year = int(groups[1])
                            month = month_map.get(groups[0][:3], 1)
                        else:  # Year Month
                            year = int(groups[0])
                            month = month_map.get(groups[1][:3], 1)

                        return {"year": year, "month": month, "day": 1}

                    elif len(groups) == 1:  # Just year
                        year = int(groups[0])
                        if 1990 <= year <= 2030:
                            return {"year": year, "month": 1, "day": 1}

                except ValueError:
                    continue

        return None

    def calculate_confidence_scores(self, result: Dict[str, Any], doc) -> Dict[str, float]:
        """Calculate confidence scores based on spaCy analysis"""
        scores = {}

        # Name confidence (based on PERSON entities found)
        person_entities = [ent for ent in doc.ents if ent.label_ == "PERSON"]
        scores["name"] = 1.0 if result["name"] and person_entities else (0.5 if result["name"] else 0.0)

        # Email confidence
        scores["email"] = 1.0 if result["email"] else 0.0

        # Phone confidence
        scores["phone"] = 1.0 if result["phone"] else 0.0

        # Address confidence (based on location entities)
        location_entities = [ent for ent in doc.ents if ent.label_ in ["GPE", "LOC"]]
        address_fields = [v for v in result["address"].values() if v]
        scores["address"] = min(1.0, (len(address_fields) / 4.0) + (len(location_entities) * 0.1))

        # Skills confidence
        scores["skills"] = min(1.0, len(result["skills"]) / 15.0)

        # Education confidence (based on ORG entities and education patterns)
        org_entities = [ent for ent in doc.ents if ent.label_ == "ORG"]
        scores["education"] = min(1.0, (len(result["education"]) / 3.0) + (len(org_entities) * 0.1))

        # Experience confidence
        scores["experience"] = min(1.0, len(result["experience"]) / 3.0)

        # Overall confidence
        scores["overall"] = sum(scores.values()) / len(scores)

        return {k: round(v, 2) for k, v in scores.items()}

def test_spacy_parser():
    """Test the spaCy parser with sample text"""
    sample_text = """
    John Doe
    Senior Angular Developer
    <EMAIL>
    +91 9876543210
    Mumbai, India

    Experience:
    Senior Angular Developer at Tech Solutions Pvt Ltd
    June 2021 - March 2023
    - Developed Angular applications using TypeScript
    - Implemented responsive designs with Bootstrap

    Education:
    B.Tech in Computer Science, XYZ University, 2020

    Skills:
    Angular, TypeScript, JavaScript, Node.js, MongoDB, AWS
    """

    try:
        parser = SpacyResumeParser()
        result = parser.parse_resume(sample_text)

        print("spaCy Parser Results:")
        print(json.dumps(result, indent=2))
        return result
    except Exception as e:
        print(f"Error testing spaCy parser: {e}")
        print("Make sure to install spaCy and download the model:")
        print("pip install spacy")
        print("python -m spacy download en_core_web_sm")
        return None

if __name__ == "__main__":
    test_spacy_parser()