# Angular Developer Resume Parsing Project

## 📌 Project Overview
This project explores how to **collect publicly available Angular developer resumes (PDF format)** and then convert them into structured **JSON format** using multiple offline/local approaches.  
The focus is on testing techniques that do **not rely on paid APIs (like OpenAI or SpaCy Pro)** and instead use open-source Python libraries or rule-based parsing.

---

## 🛠️ Project Phases

### Phase 1: Collect Resumes
- Identify **publicly available** resume PDFs (safe sources only, such as sample resumes from GitHub, google, or free resume templates).
- Download at least **10 Angular Developer resumes** into a local folder (`resumes/`).
- Tools:
  - `requests` (to download files from a URL list)

---

### Phase 2: Text Extraction from PDFs
Test multiple local libraries to extract text:
1. **PyPDF2** → basic text extraction.
2. **pdfplumber** → better layout + line detection.
3. **pdfminer.six** → lower-level parsing, control over layout.
4. **Camelot / Tabula** → if resumes contain tabular structures.
5. **PyMuPDF (fitz)** → fast text + coordinates extraction.

---

### Phase 3: Handle Scanned PDFs
- Some resumes might be **scanned images** (not machine-readable text).
- Apply **Tesseract OCR** via `pytesseract` + `Pillow` to extract text.
- Detect whether a page is scanned vs text before deciding extraction method.

---

### Phase 4: Parse Resume Data (Entity Extraction)
Convert free-form text into structured JSON fields:
 Try Differnt Approaches which shoudl cost anything , should be 0
 try 3 to 4 ways and generate what we did  accuaracy of the ways
if we are going to train a model give steps 


---

### Phase 5: Convert to JSON
For each resume, generate a structured JSON object:
Object should cover all things a resume have

```json
{
  "resume_id": "unique_id",
  "address":{
    "country":"",
    "state":"",
    "city":"",
    "pincode":""
  },
  "name": "Candidate Name",
  "email": "<EMAIL>",
  "phone": "+91 9876543210",
  "skills": ["Angular", "TypeScript", "Node.js"],
  "education": ["B.Tech in Computer Science, 2020, XYZ University"],
  "experience": [
    {
      "company": "ABC Corp", 
       "role": "Frontend Developer",
       "start_date": {
          "year": 2021,
          "month": 6,
          "day": 1
       },
       "end_date": {
          "year": 2023,
          "month": 3,
          "day": 31
       },
      "description":"",
      "responsibilities": ["Developed Angular applications", "Implemented TypeScript features"]

    }
  ]
}
