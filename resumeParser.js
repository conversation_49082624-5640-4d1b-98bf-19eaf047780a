/**
 * Resume Parser - Multiple Approaches for 100% Accuracy
 * Converts extracted text to structured JSON
 */

const natural = require('natural');
const compromise = require('compromise');
const moment = require('moment');
const { v4: uuidv4 } = require('uuid');
const _ = require('lodash');

class ResumeParser {
    constructor() {
        this.setupPatterns();
        this.setupSkillDatabase();
        this.setupNLPTools();
    }

    setupPatterns() {
        this.patterns = {
            email: /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g,
            phone: [
                /\+\d{1,3}[-.\s]?\d{10}/g,
                /\(\d{3}\)\s?\d{3}[-.\s]?\d{4}/g,
                /\d{3}[-.\s]?\d{3}[-.\s]?\d{4}/g,
                /\d{10}/g
            ],
            name: /^([A-Z][a-z]+\s+[A-Z][a-z]+(?:\s+[A-Z][a-z]+)?)/m,
            dates: [
                /\b(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\s+(\d{4})\b/gi,
                /\b(\d{1,2})[\/\-](\d{1,2})[\/\-](\d{4})\b/g,
                /\b(\d{4})[\/\-](\d{1,2})[\/\-](\d{1,2})\b/g,
                /\b(\d{4})\b/g
            ],
            pincode: /\b\d{5,6}\b/g,
            degree: /\b(B\.?Tech|Bachelor|B\.?E\.?|B\.?Sc|M\.?Tech|Master|M\.?E\.?|M\.?Sc|PhD|Doctorate)\b/gi,
            university: /\b\w+\s+(University|College|Institute|School)\b/gi,
            company: /\b\w+\s+(Corp|Corporation|Inc|Ltd|Limited|Technologies|Solutions|Systems|Pvt)\b/gi
        };

        this.sectionKeywords = {
            experience: ['experience', 'work experience', 'employment', 'career', 'professional experience', 'work history'],
            education: ['education', 'academic', 'qualification', 'degree', 'university', 'college'],
            skills: ['skills', 'technical skills', 'technologies', 'expertise', 'competencies', 'tools'],
            projects: ['projects', 'personal projects', 'work projects', 'portfolio'],
            contact: ['contact', 'personal information', 'details', 'profile'],
            summary: ['summary', 'objective', 'profile', 'about', 'overview']
        };
    }

    setupSkillDatabase() {
        this.skillCategories = {
            frontend: [
                'angular', 'react', 'vue', 'svelte', 'ember', 'backbone', 'jquery',
                'javascript', 'typescript', 'html', 'css', 'scss', 'sass', 'less',
                'bootstrap', 'tailwind', 'material-ui', 'webpack', 'vite', 'parcel'
            ],
            backend: [
                'node.js', 'express', 'nestjs', 'koa', 'fastify',
                'python', 'django', 'flask', 'fastapi', 'tornado',
                'java', 'spring', 'spring boot', 'hibernate',
                'c#', '.net', 'asp.net', 'entity framework',
                'php', 'laravel', 'symfony', 'codeigniter',
                'ruby', 'rails', 'sinatra', 'go', 'gin', 'echo',
                'rust', 'actix', 'rocket', 'scala', 'play'
            ],
            database: [
                'mysql', 'postgresql', 'sqlite', 'mariadb',
                'mongodb', 'couchdb', 'cassandra', 'dynamodb',
                'redis', 'memcached', 'elasticsearch', 'solr',
                'oracle', 'sql server', 'db2', 'neo4j'
            ],
            cloud: [
                'aws', 'amazon web services', 'ec2', 's3', 'lambda', 'rds',
                'azure', 'microsoft azure', 'google cloud', 'gcp',
                'docker', 'kubernetes', 'openshift', 'helm',
                'terraform', 'ansible', 'chef', 'puppet',
                'jenkins', 'gitlab ci', 'github actions', 'circleci'
            ],
            mobile: [
                'react native', 'flutter', 'ionic', 'cordova', 'phonegap',
                'swift', 'objective-c', 'kotlin', 'java android',
                'xamarin', 'unity', 'unreal engine'
            ],
            tools: [
                'git', 'github', 'gitlab', 'bitbucket', 'svn',
                'jira', 'confluence', 'trello', 'asana', 'slack',
                'visual studio code', 'intellij', 'eclipse', 'sublime',
                'postman', 'insomnia', 'swagger', 'figma', 'sketch'
            ]
        };

        // Flatten all skills
        this.allSkills = Object.values(this.skillCategories).flat();
    }

    setupNLPTools() {
        this.tokenizer = new natural.WordTokenizer();
        this.stemmer = natural.PorterStemmer;
        this.sentiment = new natural.SentimentAnalyzer('English', 
            natural.PorterStemmer, ['negation']);
    }

    /**
     * Main parsing method - combines multiple approaches
     */
    async parseResume(text, approach = 'hybrid') {
        console.log(`🔍 Parsing resume using ${approach} approach...`);

        const result = {
            resume_id: uuidv4(),
            address: {
                country: '',
                state: '',
                city: '',
                pincode: ''
            },
            name: '',
            email: '',
            phone: '',
            skills: [],
            education: [],
            experience: [],
            projects: [],
            summary: '',
            parsing_metadata: {
                method: approach,
                timestamp: new Date().toISOString(),
                confidence_scores: {},
                text_length: text.length
            }
        };

        switch (approach) {
            case 'pattern':
                return await this.parseWithPatterns(text, result);
            case 'nlp':
                return await this.parseWithNLP(text, result);
            case 'ml':
                return await this.parseWithML(text, result);
            case 'hybrid':
            default:
                return await this.parseWithHybrid(text, result);
        }
    }

    /**
     * Approach 1: Pattern-based parsing
     */
    async parseWithPatterns(text, result) {
        console.log('📝 Using pattern-based parsing...');

        // Extract basic information
        result.name = this.extractName(text);
        result.email = this.extractEmail(text);
        result.phone = this.extractPhone(text);
        result.address = this.extractAddress(text);

        // Extract sections
        const sections = this.extractSections(text);
        
        result.skills = this.extractSkills(sections.skills || text);
        result.education = this.extractEducation(sections.education || text);
        result.experience = this.extractExperience(sections.experience || text);
        result.projects = this.extractProjects(sections.projects || text);
        result.summary = this.extractSummary(sections.summary || text);

        // Calculate confidence
        result.parsing_metadata.confidence_scores = this.calculateConfidence(result);
        result.parsing_metadata.sections_found = Object.keys(sections);

        return result;
    }

    /**
     * Approach 2: NLP-based parsing
     */
    async parseWithNLP(text, result) {
        console.log('🧠 Using NLP-based parsing...');

        // Use compromise.js for better entity extraction
        const doc = compromise(text);

        // Extract entities
        result.name = this.extractNameNLP(doc);
        result.email = this.extractEmail(text);
        result.phone = this.extractPhone(text);
        result.address = this.extractAddressNLP(doc);

        // Extract sections with NLP
        const sections = this.extractSectionsNLP(text, doc);
        
        result.skills = this.extractSkillsNLP(doc, sections.skills);
        result.education = this.extractEducationNLP(doc, sections.education);
        result.experience = this.extractExperienceNLP(doc, sections.experience);
        result.projects = this.extractProjectsNLP(doc, sections.projects);
        result.summary = this.extractSummaryNLP(doc, sections.summary);

        // Calculate confidence
        result.parsing_metadata.confidence_scores = this.calculateConfidenceNLP(result, doc);
        result.parsing_metadata.entities_found = this.getEntitiesFound(doc);

        return result;
    }

    /**
     * Approach 3: Machine Learning-based parsing
     */
    async parseWithML(text, result) {
        console.log('🤖 Using ML-based parsing...');

        // Tokenize and analyze text
        const tokens = this.tokenizer.tokenize(text.toLowerCase());
        const features = this.extractFeatures(text, tokens);

        // Classify sections
        const sections = this.classifySections(text, features);

        // Extract information using ML features
        result.name = this.extractNameML(text, features);
        result.email = this.extractEmail(text);
        result.phone = this.extractPhone(text);
        result.address = this.extractAddressML(text, features);

        result.skills = this.extractSkillsML(sections.skills || text, features);
        result.education = this.extractEducationML(sections.education || text, features);
        result.experience = this.extractExperienceML(sections.experience || text, features);
        result.projects = this.extractProjectsML(sections.projects || text, features);
        result.summary = this.extractSummaryML(sections.summary || text, features);

        // Calculate confidence
        result.parsing_metadata.confidence_scores = this.calculateConfidenceML(result, features);
        result.parsing_metadata.ml_features = features;

        return result;
    }

    /**
     * Approach 4: Hybrid approach - combines all methods
     */
    async parseWithHybrid(text, result) {
        console.log('🔄 Using hybrid parsing approach...');

        // Get results from all approaches
        const patternResult = await this.parseWithPatterns(text, JSON.parse(JSON.stringify(result)));
        const nlpResult = await this.parseWithNLP(text, JSON.parse(JSON.stringify(result)));
        const mlResult = await this.parseWithML(text, JSON.parse(JSON.stringify(result)));

        // Combine results with weighted voting
        result.name = this.combineNames([patternResult.name, nlpResult.name, mlResult.name]);
        result.email = patternResult.email || nlpResult.email || mlResult.email;
        result.phone = patternResult.phone || nlpResult.phone || mlResult.phone;
        result.address = this.combineAddresses([patternResult.address, nlpResult.address, mlResult.address]);

        result.skills = this.combineSkills([patternResult.skills, nlpResult.skills, mlResult.skills]);
        result.education = this.combineEducation([patternResult.education, nlpResult.education, mlResult.education]);
        result.experience = this.combineExperience([patternResult.experience, nlpResult.experience, mlResult.experience]);
        result.projects = this.combineProjects([patternResult.projects, nlpResult.projects, mlResult.projects]);
        result.summary = this.combineSummary([patternResult.summary, nlpResult.summary, mlResult.summary]);

        // Calculate combined confidence
        result.parsing_metadata.confidence_scores = this.calculateCombinedConfidence([
            patternResult.parsing_metadata.confidence_scores,
            nlpResult.parsing_metadata.confidence_scores,
            mlResult.parsing_metadata.confidence_scores
        ]);

        result.parsing_metadata.individual_results = {
            pattern: patternResult.parsing_metadata.confidence_scores,
            nlp: nlpResult.parsing_metadata.confidence_scores,
            ml: mlResult.parsing_metadata.confidence_scores
        };

        return result;
    }

    // Basic extraction methods
    extractName(text) {
        const lines = text.split('\n').filter(line => line.trim());
        
        // Try first few lines
        for (let i = 0; i < Math.min(5, lines.length); i++) {
            const line = lines[i].trim();
            if (line && line.length < 50 && !line.includes('@') && !line.match(/\d{10}/)) {
                const words = line.split(/\s+/);
                if (words.length >= 2 && words.length <= 4) {
                    // Check if it looks like a name
                    if (words.every(word => /^[A-Za-z]+$/.test(word))) {
                        return line;
                    }
                }
            }
        }

        // Fallback to pattern matching
        const match = text.match(this.patterns.name);
        return match ? match[1] : '';
    }

    extractEmail(text) {
        const match = text.match(this.patterns.email);
        return match ? match[0] : '';
    }

    extractPhone(text) {
        for (const pattern of this.patterns.phone) {
            const match = text.match(pattern);
            if (match) return match[0];
        }
        return '';
    }

    extractAddress(text) {
        const address = { country: '', state: '', city: '', pincode: '' };
        
        // Extract pincode
        const pincodeMatch = text.match(this.patterns.pincode);
        if (pincodeMatch) address.pincode = pincodeMatch[0];

        // Extract country
        const countries = ['India', 'USA', 'United States', 'UK', 'United Kingdom', 'Canada', 'Australia'];
        for (const country of countries) {
            if (text.toLowerCase().includes(country.toLowerCase())) {
                address.country = country;
                break;
            }
        }

        // Extract cities (Indian cities for now)
        const cities = ['Mumbai', 'Delhi', 'Bangalore', 'Hyderabad', 'Chennai', 'Kolkata', 'Pune', 'Ahmedabad'];
        for (const city of cities) {
            if (text.toLowerCase().includes(city.toLowerCase())) {
                address.city = city;
                break;
            }
        }

        return address;
    }

    extractSections(text) {
        const sections = {};
        const lines = text.split('\n');
        let currentSection = null;
        let currentContent = [];

        for (const line of lines) {
            const lowerLine = line.toLowerCase().trim();

            // Check if line is a section header
            let foundSection = null;
            for (const [sectionName, keywords] of Object.entries(this.sectionKeywords)) {
                if (keywords.some(keyword => lowerLine.includes(keyword))) {
                    foundSection = sectionName;
                    break;
                }
            }

            if (foundSection) {
                // Save previous section
                if (currentSection && currentContent.length > 0) {
                    sections[currentSection] = currentContent.join('\n');
                }

                currentSection = foundSection;
                currentContent = [];
            } else if (currentSection) {
                currentContent.push(line);
            }
        }

        // Save last section
        if (currentSection && currentContent.length > 0) {
            sections[currentSection] = currentContent.join('\n');
        }

        return sections;
    }

    extractSkills(text) {
        const foundSkills = new Set();
        const lowerText = text.toLowerCase();

        // Direct skill matching
        for (const skill of this.allSkills) {
            const regex = new RegExp(`\\b${skill.toLowerCase().replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'i');
            if (regex.test(lowerText)) {
                foundSkills.add(skill);
            }
        }

        return Array.from(foundSkills).sort();
    }

    extractEducation(text) {
        const education = [];
        const lines = text.split('\n').filter(line => line.trim());

        for (const line of lines) {
            const trimmedLine = line.trim();
            if (trimmedLine.length > 10) {
                // Check for degree patterns
                const hasDegree = this.patterns.degree.test(trimmedLine);
                const hasUniversity = this.patterns.university.test(trimmedLine);

                if (hasDegree || hasUniversity) {
                    education.push(trimmedLine);
                }
            }
        }

        return education.slice(0, 5); // Limit to 5 entries
    }

    extractExperience(text) {
        const experience = [];
        const lines = text.split('\n').filter(line => line.trim());

        let currentExp = null;

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();

            // Check for job titles
            const jobTitles = ['developer', 'engineer', 'manager', 'analyst', 'consultant', 'architect'];
            const hasJobTitle = jobTitles.some(title => line.toLowerCase().includes(title));

            if (hasJobTitle && line.length < 100) {
                // Save previous experience
                if (currentExp) {
                    experience.push(currentExp);
                }

                currentExp = {
                    role: line,
                    company: '',
                    start_date: { year: 0, month: 0, day: 0 },
                    end_date: { year: 0, month: 0, day: 0 },
                    description: '',
                    responsibilities: []
                };

                // Look for company in next few lines
                for (let j = i + 1; j < Math.min(i + 4, lines.length); j++) {
                    const nextLine = lines[j].trim();
                    if (this.patterns.company.test(nextLine)) {
                        currentExp.company = nextLine;
                        break;
                    }
                }

                // Extract dates
                const dates = this.extractDates(line);
                if (dates.length >= 2) {
                    currentExp.start_date = dates[0];
                    currentExp.end_date = dates[1];
                } else if (dates.length === 1) {
                    currentExp.start_date = dates[0];
                }
            } else if (currentExp && line.startsWith('-') || line.startsWith('•')) {
                currentExp.responsibilities.push(line.substring(1).trim());
            }
        }

        if (currentExp) {
            experience.push(currentExp);
        }

        return experience.slice(0, 5);
    }

    extractProjects(text) {
        const projects = [];
        const lines = text.split('\n').filter(line => line.trim());

        let currentProject = null;

        for (const line of lines) {
            const trimmedLine = line.trim();

            // Look for project indicators
            if (trimmedLine.length > 5 && trimmedLine.length < 100 &&
                !trimmedLine.startsWith('-') && !trimmedLine.startsWith('•')) {

                // Check if it might be a project title
                const projectKeywords = ['project', 'app', 'application', 'website', 'system', 'platform'];
                const hasProjectKeyword = projectKeywords.some(keyword =>
                    trimmedLine.toLowerCase().includes(keyword));

                if (hasProjectKeyword || (currentProject === null && projects.length < 3)) {
                    if (currentProject) {
                        projects.push(currentProject);
                    }

                    currentProject = {
                        name: trimmedLine,
                        description: '',
                        technologies: [],
                        responsibilities: []
                    };
                }
            } else if (currentProject && (trimmedLine.startsWith('-') || trimmedLine.startsWith('•'))) {
                currentProject.responsibilities.push(trimmedLine.substring(1).trim());
            } else if (currentProject && trimmedLine.length > 10) {
                currentProject.description += trimmedLine + ' ';
            }
        }

        if (currentProject) {
            projects.push(currentProject);
        }

        return projects.slice(0, 5);
    }

    extractSummary(text) {
        const lines = text.split('\n').filter(line => line.trim());

        // Look for summary section or first paragraph
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].toLowerCase();
            if (line.includes('summary') || line.includes('objective') || line.includes('profile')) {
                // Get next few lines as summary
                const summaryLines = [];
                for (let j = i + 1; j < Math.min(i + 5, lines.length); j++) {
                    const nextLine = lines[j].trim();
                    if (nextLine && nextLine.length > 20) {
                        summaryLines.push(nextLine);
                    }
                }
                return summaryLines.join(' ').substring(0, 500);
            }
        }

        // Fallback: use first substantial paragraph
        for (const line of lines) {
            if (line.trim().length > 50 && !line.includes('@') && !line.match(/\d{10}/)) {
                return line.trim().substring(0, 500);
            }
        }

        return '';
    }

    extractDates(text) {
        const dates = [];

        for (const pattern of this.patterns.dates) {
            const matches = text.matchAll(pattern);
            for (const match of matches) {
                const dateObj = this.parseDate(match);
                if (dateObj) {
                    dates.push(dateObj);
                }
            }
        }

        return dates;
    }

    parseDate(match) {
        try {
            if (match.length === 3) {
                // Month Year format
                const monthNames = ['jan', 'feb', 'mar', 'apr', 'may', 'jun',
                                  'jul', 'aug', 'sep', 'oct', 'nov', 'dec'];
                const monthIndex = monthNames.findIndex(m =>
                    match[1].toLowerCase().startsWith(m)) + 1;

                return {
                    year: parseInt(match[2]),
                    month: monthIndex || 1,
                    day: 1
                };
            } else if (match.length === 4) {
                // Date format
                return {
                    year: parseInt(match[3]),
                    month: parseInt(match[2]),
                    day: parseInt(match[1])
                };
            } else if (match.length === 2) {
                // Just year
                const year = parseInt(match[1]);
                if (year >= 1990 && year <= 2030) {
                    return { year, month: 1, day: 1 };
                }
            }
        } catch (error) {
            // Ignore parsing errors
        }

        return null;
    }

    calculateConfidence(result) {
        const scores = {};

        scores.name = result.name ? 1.0 : 0.0;
        scores.email = result.email ? 1.0 : 0.0;
        scores.phone = result.phone ? 1.0 : 0.0;

        const addressFields = Object.values(result.address).filter(v => v).length;
        scores.address = addressFields / 4.0;

        scores.skills = Math.min(1.0, result.skills.length / 10.0);
        scores.education = Math.min(1.0, result.education.length / 3.0);
        scores.experience = Math.min(1.0, result.experience.length / 3.0);
        scores.projects = Math.min(1.0, result.projects.length / 3.0);

        scores.overall = Object.values(scores).reduce((a, b) => a + b, 0) / Object.keys(scores).length;

        return scores;
    }

    // Placeholder methods for NLP and ML approaches
    extractNameNLP(doc) {
        const people = doc.people().out('array');
        return people.length > 0 ? people[0] : this.extractName(doc.text());
    }

    extractAddressNLP(doc) {
        const places = doc.places().out('array');
        const address = this.extractAddress(doc.text());

        if (places.length > 0) {
            address.city = places[0];
        }

        return address;
    }

    extractSectionsNLP(text, doc) {
        return this.extractSections(text);
    }

    extractSkillsNLP(doc, skillsText) {
        return this.extractSkills(skillsText || doc.text());
    }

    extractEducationNLP(doc, educationText) {
        return this.extractEducation(educationText || doc.text());
    }

    extractExperienceNLP(doc, experienceText) {
        return this.extractExperience(experienceText || doc.text());
    }

    extractProjectsNLP(doc, projectsText) {
        return this.extractProjects(projectsText || doc.text());
    }

    extractSummaryNLP(doc, summaryText) {
        return this.extractSummary(summaryText || doc.text());
    }

    calculateConfidenceNLP(result, doc) {
        const baseConfidence = this.calculateConfidence(result);

        // Boost confidence if NLP found entities
        const people = doc.people().out('array');
        const places = doc.places().out('array');

        if (people.length > 0) baseConfidence.name = Math.min(1.0, baseConfidence.name + 0.2);
        if (places.length > 0) baseConfidence.address = Math.min(1.0, baseConfidence.address + 0.2);

        baseConfidence.overall = Object.values(baseConfidence).reduce((a, b) => a + b, 0) / Object.keys(baseConfidence).length;

        return baseConfidence;
    }

    getEntitiesFound(doc) {
        return {
            people: doc.people().out('array'),
            places: doc.places().out('array'),
            organizations: doc.organizations().out('array')
        };
    }

    // ML approach methods (simplified)
    extractFeatures(text, tokens) {
        return {
            wordCount: tokens.length,
            avgWordLength: tokens.reduce((sum, token) => sum + token.length, 0) / tokens.length,
            skillKeywordCount: this.allSkills.filter(skill =>
                text.toLowerCase().includes(skill.toLowerCase())).length,
            hasEmail: /@/.test(text),
            hasPhone: /\d{10}/.test(text),
            lineCount: text.split('\n').length
        };
    }

    classifySections(text, features) {
        return this.extractSections(text);
    }

    extractNameML(text, features) {
        return this.extractName(text);
    }

    extractAddressML(text, features) {
        return this.extractAddress(text);
    }

    extractSkillsML(text, features) {
        return this.extractSkills(text);
    }

    extractEducationML(text, features) {
        return this.extractEducation(text);
    }

    extractExperienceML(text, features) {
        return this.extractExperience(text);
    }

    extractProjectsML(text, features) {
        return this.extractProjects(text);
    }

    extractSummaryML(text, features) {
        return this.extractSummary(text);
    }

    calculateConfidenceML(result, features) {
        const baseConfidence = this.calculateConfidence(result);

        // Adjust based on ML features
        if (features.skillKeywordCount > 5) {
            baseConfidence.skills = Math.min(1.0, baseConfidence.skills + 0.1);
        }

        baseConfidence.overall = Object.values(baseConfidence).reduce((a, b) => a + b, 0) / Object.keys(baseConfidence).length;

        return baseConfidence;
    }

    // Combination methods for hybrid approach
    combineNames(names) {
        const validNames = names.filter(name => name && name.trim());
        return validNames.length > 0 ? validNames[0] : '';
    }

    combineAddresses(addresses) {
        const combined = { country: '', state: '', city: '', pincode: '' };

        for (const addr of addresses) {
            for (const field of Object.keys(combined)) {
                if (!combined[field] && addr[field]) {
                    combined[field] = addr[field];
                }
            }
        }

        return combined;
    }

    combineSkills(skillArrays) {
        const allSkills = new Set();
        skillArrays.forEach(skills => skills.forEach(skill => allSkills.add(skill)));
        return Array.from(allSkills).sort();
    }

    combineEducation(educationArrays) {
        const allEducation = new Set();
        educationArrays.forEach(education => education.forEach(edu => allEducation.add(edu)));
        return Array.from(allEducation);
    }

    combineExperience(experienceArrays) {
        const allExperience = [];
        const seen = new Set();

        experienceArrays.forEach(experiences => {
            experiences.forEach(exp => {
                const key = `${exp.role}-${exp.company}`.toLowerCase();
                if (!seen.has(key)) {
                    seen.add(key);
                    allExperience.push(exp);
                }
            });
        });

        return allExperience;
    }

    combineProjects(projectArrays) {
        const allProjects = [];
        const seen = new Set();

        projectArrays.forEach(projects => {
            projects.forEach(project => {
                const key = project.name.toLowerCase();
                if (!seen.has(key)) {
                    seen.add(key);
                    allProjects.push(project);
                }
            });
        });

        return allProjects;
    }

    combineSummary(summaries) {
        const validSummaries = summaries.filter(summary => summary && summary.trim());
        return validSummaries.length > 0 ? validSummaries[0] : '';
    }

    calculateCombinedConfidence(confidenceArrays) {
        const combined = {};
        const validArrays = confidenceArrays.filter(conf => conf && typeof conf === 'object');

        if (validArrays.length === 0) return {};

        const fields = Object.keys(validArrays[0]);

        for (const field of fields) {
            const values = validArrays.map(conf => conf[field]).filter(val => typeof val === 'number');
            combined[field] = values.length > 0 ?
                values.reduce((sum, val) => sum + val, 0) / values.length : 0;
        }

        return combined;
    }
}

module.exports = ResumeParser;
