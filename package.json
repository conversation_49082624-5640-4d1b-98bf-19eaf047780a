{"name": "resume-parser-nodejs", "version": "1.0.0", "description": "Node.js Resume Parser with Multiple Approaches for 100% Accuracy", "main": "index.js", "scripts": {"start": "node index.js", "test": "node test.js", "seed": "node seed.js", "parse": "node parseResume.js", "evaluate": "node evaluate.js"}, "keywords": ["resume", "parser", "pdf", "json", "nodejs"], "author": "Resume Parser Team", "license": "MIT", "dependencies": {"pdf-parse": "^1.1.1", "pdf2pic": "^2.1.4", "tesseract.js": "^4.1.1", "natural": "^6.5.0", "compromise": "^14.10.0", "axios": "^1.6.0", "cheerio": "^1.0.0-rc.12", "fs-extra": "^11.1.1", "path": "^0.12.7", "uuid": "^9.0.1", "moment": "^2.29.4", "lodash": "^4.17.21", "puppeteer": "^21.5.0", "node-cron": "^3.0.3"}, "devDependencies": {"nodemon": "^3.0.1"}}