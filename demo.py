"""
Demonstration Script for Resume Parsing Project
Shows all 4 approaches with sample data and performance comparison
"""

import json
import time
from typing import Dict, Any
import logging

# Configure logging
logging.basicConfig(level=logging.WARNING)  # Reduce noise for demo

def print_header(title: str):
    """Print a formatted header"""
    print("\n" + "="*60)
    print(f"  {title}")
    print("="*60)

def print_section(title: str):
    """Print a formatted section header"""
    print(f"\n📋 {title}")
    print("-" * 40)

def print_results(approach_name: str, result: Dict[str, Any], parse_time: float):
    """Print parsing results in a formatted way"""
    print(f"\n🔍 {approach_name.upper()} RESULTS:")
    print(f"   ⏱️  Parse Time: {parse_time:.3f} seconds")
    
    if 'error' in result:
        print(f"   ❌ Error: {result['error']}")
        return
    
    print(f"   👤 Name: {result.get('name', 'Not found')}")
    print(f"   📧 Email: {result.get('email', 'Not found')}")
    print(f"   📞 Phone: {result.get('phone', 'Not found')}")
    print(f"   🏠 Address: {result.get('address', {}).get('city', 'Not found')}")
    print(f"   🛠️  Skills: {len(result.get('skills', []))} found - {', '.join(result.get('skills', [])[:5])}")
    print(f"   🎓 Education: {len(result.get('education', []))} entries")
    print(f"   💼 Experience: {len(result.get('experience', []))} entries")
    
    # Show confidence scores if available
    if 'parsing_metadata' in result and 'confidence_scores' in result['parsing_metadata']:
        scores = result['parsing_metadata']['confidence_scores']
        print(f"   📊 Overall Confidence: {scores.get('overall', 0):.1%}")

def demo_all_approaches():
    """Demonstrate all parsing approaches"""
    
    print_header("RESUME PARSING PROJECT - LIVE DEMONSTRATION")
    
    # Sample resume text
    sample_resume = """
    John Doe
    Senior Angular Developer
    <EMAIL>
    +91 9876543210
    Mumbai, India 400001
    
    Professional Summary
    Experienced Angular developer with 5+ years in frontend development
    
    Work Experience
    Senior Angular Developer at Tech Solutions Pvt Ltd
    June 2021 - March 2023
    - Developed Angular applications using TypeScript
    - Implemented responsive designs with Bootstrap
    - Led a team of 3 developers
    - Integrated REST APIs and GraphQL
    
    Software Engineer at StartupCorp Inc
    January 2019 - May 2021
    - Built React applications with Redux
    - Worked with Node.js backend services
    - Implemented unit testing with Jest
    
    Education
    Bachelor of Technology in Computer Science
    Indian Institute of Technology Delhi
    2015-2019
    CGPA: 8.5/10
    
    Technical Skills
    Frontend: Angular, React, Vue.js, TypeScript, JavaScript, HTML5, CSS3, SCSS
    Backend: Node.js, Express.js, Python, Django
    Databases: MongoDB, MySQL, PostgreSQL, Redis
    Cloud: AWS, Azure, Docker, Kubernetes
    Tools: Git, Jenkins, Jira, Postman, Webpack
    
    Projects
    E-commerce Platform (2022)
    - Built using Angular 14 and Node.js
    - Implemented payment gateway integration
    - Used MongoDB for data storage
    
    Task Management App (2021)
    - React-based SPA with real-time updates
    - WebSocket integration for live notifications
    """
    
    print_section("SAMPLE RESUME TEXT")
    print("📄 Processing the following resume:")
    print(f"   Length: {len(sample_resume)} characters")
    print(f"   Preview: {sample_resume[:200]}...")
    
    # Initialize results storage
    results = {}
    
    # Approach 1: Rule-based
    print_section("APPROACH 1: RULE-BASED PATTERN MATCHING")
    try:
        from txt2Json.apporach1 import RuleBasedResumeParser
        
        parser = RuleBasedResumeParser()
        start_time = time.time()
        result = parser.parse_resume(sample_resume)
        parse_time = time.time() - start_time
        
        results['rule_based'] = {'result': result, 'time': parse_time}
        print_results("Rule-based", result, parse_time)
        
    except Exception as e:
        print(f"❌ Rule-based approach failed: {e}")
        results['rule_based'] = {'error': str(e), 'time': 0}
    
    # Approach 2: spaCy NLP
    print_section("APPROACH 2: SPACY NLP")
    try:
        from txt2Json.approach2 import SpacyResumeParser
        
        parser = SpacyResumeParser()
        start_time = time.time()
        result = parser.parse_resume(sample_resume)
        parse_time = time.time() - start_time
        
        results['spacy'] = {'result': result, 'time': parse_time}
        print_results("spaCy NLP", result, parse_time)
        
    except Exception as e:
        print(f"⚠️  spaCy approach not available: {e}")
        print("   💡 Install with: pip install spacy && python -m spacy download en_core_web_sm")
        results['spacy'] = {'error': str(e), 'time': 0}
    
    # Approach 3: Machine Learning
    print_section("APPROACH 3: MACHINE LEARNING CLASSIFICATION")
    try:
        from txt2Json.approach3 import MLResumeParser
        
        parser = MLResumeParser()
        start_time = time.time()
        result = parser.parse_resume(sample_resume)
        parse_time = time.time() - start_time
        
        results['ml'] = {'result': result, 'time': parse_time}
        print_results("Machine Learning", result, parse_time)
        
    except Exception as e:
        print(f"❌ ML approach failed: {e}")
        results['ml'] = {'error': str(e), 'time': 0}
    
    # Approach 4: Hybrid
    print_section("APPROACH 4: HYBRID APPROACH")
    try:
        from txt2Json.approach4_hybrid import HybridResumeParser
        
        parser = HybridResumeParser()
        start_time = time.time()
        result = parser.parse_resume(sample_resume)
        parse_time = time.time() - start_time
        
        results['hybrid'] = {'result': result, 'time': parse_time}
        print_results("Hybrid", result, parse_time)
        
    except Exception as e:
        print(f"❌ Hybrid approach failed: {e}")
        results['hybrid'] = {'error': str(e), 'time': 0}
    
    # Performance Comparison
    print_section("PERFORMANCE COMPARISON")
    
    successful_approaches = {k: v for k, v in results.items() if 'result' in v}
    
    if successful_approaches:
        print("📊 COMPARISON TABLE:")
        print(f"{'Approach':<15} {'Time (s)':<10} {'Skills':<8} {'Experience':<12} {'Education':<10}")
        print("-" * 60)
        
        for approach, data in successful_approaches.items():
            result = data['result']
            skills_count = len(result.get('skills', []))
            exp_count = len(result.get('experience', []))
            edu_count = len(result.get('education', []))
            
            print(f"{approach.title():<15} {data['time']:<10.3f} {skills_count:<8} {exp_count:<12} {edu_count:<10}")
        
        # Best performer analysis
        print("\n🏆 BEST PERFORMERS:")
        
        # Fastest
        fastest = min(successful_approaches.items(), key=lambda x: x[1]['time'])
        print(f"   ⚡ Fastest: {fastest[0].title()} ({fastest[1]['time']:.3f}s)")
        
        # Most skills extracted
        most_skills = max(successful_approaches.items(), 
                         key=lambda x: len(x[1]['result'].get('skills', [])))
        skills_count = len(most_skills[1]['result'].get('skills', []))
        print(f"   🛠️  Most Skills: {most_skills[0].title()} ({skills_count} skills)")
        
        # Most comprehensive
        most_comprehensive = max(successful_approaches.items(),
                               key=lambda x: (len(x[1]['result'].get('skills', [])) + 
                                            len(x[1]['result'].get('experience', [])) + 
                                            len(x[1]['result'].get('education', []))))
        total_items = (len(most_comprehensive[1]['result'].get('skills', [])) + 
                      len(most_comprehensive[1]['result'].get('experience', [])) + 
                      len(most_comprehensive[1]['result'].get('education', [])))
        print(f"   📋 Most Comprehensive: {most_comprehensive[0].title()} ({total_items} total items)")
    
    else:
        print("❌ No approaches succeeded. Check dependencies and installation.")
    
    # Recommendations
    print_section("RECOMMENDATIONS")
    
    if len(successful_approaches) >= 2:
        print("✅ Multiple approaches working successfully!")
        print("💡 Recommendations:")
        print("   • Use 'hybrid' approach for best accuracy")
        print("   • Use 'rule_based' for fastest processing")
        print("   • Use 'spacy' for better entity recognition")
        print("   • Use 'ml' for customizable processing")
    elif len(successful_approaches) == 1:
        working_approach = list(successful_approaches.keys())[0]
        print(f"⚠️  Only {working_approach} approach is working.")
        print("💡 Consider installing missing dependencies for better results.")
    else:
        print("❌ No approaches are working. Please check:")
        print("   • Python dependencies: pip install -r requirements.txt")
        print("   • spaCy model: python -m spacy download en_core_web_sm")
        print("   • File permissions and paths")
    
    # Next Steps
    print_section("NEXT STEPS")
    print("🚀 To use this project:")
    print("   1. Place PDF resumes in 'resumes/' folder")
    print("   2. Run: python app.py parse --input resumes/your_resume.pdf")
    print("   3. Check results in 'results/' folder")
    print("   4. Run evaluation: python app.py evaluate")
    
    print("\n🔧 For customization:")
    print("   • Edit skill databases in approach files")
    print("   • Adjust confidence thresholds")
    print("   • Add new regex patterns")
    print("   • Train custom ML models")
    
    print("\n📚 For local LLM training (if needed):")
    print("   • Run: python local_llm_training.py")
    print("   • Follow generated training instructions")
    
    print_header("DEMONSTRATION COMPLETE")
    print("🎉 Thank you for trying the Resume Parsing Project!")
    print("📖 Check README.md for detailed documentation")
    
    return results

def save_demo_results(results: Dict[str, Any]):
    """Save demonstration results to file"""
    import os
    
    os.makedirs("results", exist_ok=True)
    
    with open("results/demo_results.json", "w") as f:
        # Convert results to JSON-serializable format
        json_results = {}
        for approach, data in results.items():
            if 'result' in data:
                json_results[approach] = {
                    'success': True,
                    'parse_time': data['time'],
                    'extracted_data': data['result']
                }
            else:
                json_results[approach] = {
                    'success': False,
                    'error': data.get('error', 'Unknown error'),
                    'parse_time': data.get('time', 0)
                }
        
        json.dump(json_results, f, indent=2)
    
    print(f"\n💾 Demo results saved to results/demo_results.json")

if __name__ == "__main__":
    results = demo_all_approaches()
    save_demo_results(results)
