"""
Main Application for Resume Parsing Project
Provides a unified interface to test all parsing approaches
"""

import os
import json
import argparse
from pathlib import Path
from typing import Dict, Any, Optional
import logging

# Import parsing modules
from parseText import PDFTextExtractor
from txt2Json.apporach1 import RuleBasedResumeParser
from txt2Json.approach3 import MLResumeParser
from evaluate_approaches import ResumeParsingEvaluator

# Try to import optional dependencies
try:
    from txt2Json.approach2 import SpacyResumeParser
    SPACY_AVAILABLE = True
except ImportError:
    SPACY_AVAILABLE = False
    print("⚠️  spaCy not available. Install with: pip install spacy && python -m spacy download en_core_web_sm")

try:
    from txt2Json.approach4_hybrid import HybridResumeParser
    HYBRID_AVAILABLE = True
except ImportError:
    HYBRID_AVAILABLE = False
    print("⚠️  Hybrid parser not available due to missing dependencies")

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ResumeParsingApp:
    """
    Main application class for resume parsing
    """

    def __init__(self):
        self.pdf_extractor = PDFTextExtractor()
        self.parsers = self.initialize_parsers()

    def initialize_parsers(self) -> Dict[str, Any]:
        """Initialize all available parsers"""
        parsers = {}

        # Rule-based parser (always available)
        try:
            parsers['rule_based'] = RuleBasedResumeParser()
            logger.info("✓ Rule-based parser initialized")
        except Exception as e:
            logger.error(f"✗ Rule-based parser failed: {e}")

        # spaCy parser (optional)
        if SPACY_AVAILABLE:
            try:
                parsers['spacy'] = SpacyResumeParser()
                logger.info("✓ spaCy parser initialized")
            except Exception as e:
                logger.warning(f"⚠️  spaCy parser failed: {e}")

        # ML parser (always available)
        try:
            parsers['ml'] = MLResumeParser()
            logger.info("✓ ML parser initialized")
        except Exception as e:
            logger.error(f"✗ ML parser failed: {e}")

        # Hybrid parser (optional)
        if HYBRID_AVAILABLE:
            try:
                parsers['hybrid'] = HybridResumeParser()
                logger.info("✓ Hybrid parser initialized")
            except Exception as e:
                logger.warning(f"⚠️  Hybrid parser failed: {e}")

        return parsers

    def extract_text_from_pdf(self, pdf_path: str, method: str = 'auto') -> Dict[str, Any]:
        """
        Extract text from PDF file

        Args:
            pdf_path: Path to PDF file
            method: Extraction method ('auto', 'pypdf2', 'pdfplumber', 'pymupdf', 'pdfminer', 'ocr')

        Returns:
            Extraction result with text and metadata
        """
        if not os.path.exists(pdf_path):
            raise FileNotFoundError(f"PDF file not found: {pdf_path}")

        logger.info(f"Extracting text from {pdf_path} using {method} method...")
        result = self.pdf_extractor.extract_text(pdf_path, method)

        logger.info(f"Extracted {len(result['text'])} characters using {result['method']}")
        logger.info(f"Confidence: {result['confidence']}")

        return result

    def parse_resume_text(self, text: str, approach: str = 'hybrid') -> Dict[str, Any]:
        """
        Parse resume text using specified approach

        Args:
            text: Resume text
            approach: Parsing approach ('rule_based', 'spacy', 'ml', 'hybrid')

        Returns:
            Parsed resume data
        """
        if approach not in self.parsers:
            available = list(self.parsers.keys())
            raise ValueError(f"Approach '{approach}' not available. Available: {available}")

        logger.info(f"Parsing resume using {approach} approach...")
        parser = self.parsers[approach]
        result = parser.parse_resume(text)

        logger.info(f"Parsing completed. Extracted {len(result.get('skills', []))} skills, "
                   f"{len(result.get('experience', []))} experience entries")

        return result

    def parse_resume_from_pdf(self, pdf_path: str, approach: str = 'hybrid',
                             extraction_method: str = 'auto') -> Dict[str, Any]:
        """
        Complete pipeline: PDF -> Text -> JSON

        Args:
            pdf_path: Path to PDF file
            approach: Parsing approach
            extraction_method: Text extraction method

        Returns:
            Complete parsing result
        """
        # Extract text
        extraction_result = self.extract_text_from_pdf(pdf_path, extraction_method)

        if not extraction_result['text'].strip():
            raise ValueError("No text could be extracted from the PDF")

        # Parse resume
        parsing_result = self.parse_resume_text(extraction_result['text'], approach)

        # Add extraction metadata
        parsing_result['parsing_metadata']['text_extraction'] = {
            'method': extraction_result['method'],
            'confidence': extraction_result['confidence'],
            'pages': extraction_result['pages'],
            'text_length': len(extraction_result['text'])
        }

        return parsing_result

    def compare_all_approaches(self, text: str) -> Dict[str, Any]:
        """
        Compare all available parsing approaches on the same text

        Args:
            text: Resume text

        Returns:
            Comparison results
        """
        logger.info("Comparing all available parsing approaches...")

        results = {
            'input_text_length': len(text),
            'approaches_tested': [],
            'results': {},
            'comparison': {}
        }

        for approach_name, parser in self.parsers.items():
            try:
                logger.info(f"Testing {approach_name}...")
                import time
                start_time = time.time()

                result = parser.parse_resume(text)
                parse_time = time.time() - start_time

                results['approaches_tested'].append(approach_name)
                results['results'][approach_name] = result

                # Add performance metrics
                results['results'][approach_name]['parsing_metadata']['parse_time'] = round(parse_time, 3)

            except Exception as e:
                logger.error(f"Error with {approach_name}: {e}")
                results['results'][approach_name] = {'error': str(e)}

        # Generate comparison
        results['comparison'] = self.generate_comparison(results['results'])

        return results

    def generate_comparison(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate comparison metrics between approaches"""
        comparison = {
            'field_extraction_count': {},
            'confidence_scores': {},
            'performance': {}
        }

        for approach, result in results.items():
            if 'error' in result:
                continue

            # Count extracted fields
            comparison['field_extraction_count'][approach] = {
                'name': 1 if result.get('name') else 0,
                'email': 1 if result.get('email') else 0,
                'phone': 1 if result.get('phone') else 0,
                'skills': len(result.get('skills', [])),
                'education': len(result.get('education', [])),
                'experience': len(result.get('experience', []))
            }

            # Confidence scores
            if 'parsing_metadata' in result and 'confidence_scores' in result['parsing_metadata']:
                comparison['confidence_scores'][approach] = result['parsing_metadata']['confidence_scores']

            # Performance
            if 'parsing_metadata' in result and 'parse_time' in result['parsing_metadata']:
                comparison['performance'][approach] = result['parsing_metadata']['parse_time']

        return comparison

    def save_result(self, result: Dict[str, Any], output_path: str):
        """Save parsing result to JSON file"""
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)

        logger.info(f"Result saved to {output_path}")

    def run_evaluation(self) -> Dict[str, Any]:
        """Run comprehensive evaluation of all approaches"""
        evaluator = ResumeParsingEvaluator()
        results = evaluator.evaluate_all_approaches()
        evaluator.print_summary(results)
        return results

def main():
    """Main CLI interface"""
    parser = argparse.ArgumentParser(description="Resume Parsing Application")
    parser.add_argument('command', choices=['parse', 'compare', 'evaluate', 'extract'],
                       help='Command to execute')
    parser.add_argument('--input', '-i', required=False, help='Input PDF file path')
    parser.add_argument('--text', '-t', required=False, help='Input text file path')
    parser.add_argument('--output', '-o', default='results', help='Output directory')
    parser.add_argument('--approach', '-a', default='hybrid',
                       choices=['rule_based', 'spacy', 'ml', 'hybrid'],
                       help='Parsing approach to use')
    parser.add_argument('--extraction', '-e', default='auto',
                       choices=['auto', 'pypdf2', 'pdfplumber', 'pymupdf', 'pdfminer', 'ocr'],
                       help='Text extraction method')

    args = parser.parse_args()

    app = ResumeParsingApp()

    if args.command == 'parse':
        if args.input:
            # Parse from PDF
            result = app.parse_resume_from_pdf(args.input, args.approach, args.extraction)
            output_file = os.path.join(args.output, f"parsed_{Path(args.input).stem}.json")
        elif args.text:
            # Parse from text file
            with open(args.text, 'r', encoding='utf-8') as f:
                text = f.read()
            result = app.parse_resume_text(text, args.approach)
            output_file = os.path.join(args.output, f"parsed_{Path(args.text).stem}.json")
        else:
            print("Error: Either --input (PDF) or --text (text file) is required for parse command")
            return

        app.save_result(result, output_file)
        print(f"✓ Parsing completed. Result saved to {output_file}")

    elif args.command == 'compare':
        if args.input:
            extraction_result = app.extract_text_from_pdf(args.input, args.extraction)
            text = extraction_result['text']
        elif args.text:
            with open(args.text, 'r', encoding='utf-8') as f:
                text = f.read()
        else:
            print("Error: Either --input (PDF) or --text (text file) is required for compare command")
            return

        result = app.compare_all_approaches(text)
        output_file = os.path.join(args.output, "comparison_results.json")
        app.save_result(result, output_file)
        print(f"✓ Comparison completed. Results saved to {output_file}")

    elif args.command == 'evaluate':
        result = app.run_evaluation()
        output_file = os.path.join(args.output, "evaluation_results.json")
        app.save_result(result, output_file)
        print(f"✓ Evaluation completed. Results saved to {output_file}")

    elif args.command == 'extract':
        if not args.input:
            print("Error: --input (PDF file) is required for extract command")
            return

        result = app.extract_text_from_pdf(args.input, args.extraction)
        output_file = os.path.join(args.output, f"extracted_{Path(args.input).stem}.txt")

        os.makedirs(args.output, exist_ok=True)
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(result['text'])

        print(f"✓ Text extraction completed. Text saved to {output_file}")
        print(f"Method used: {result['method']}, Confidence: {result['confidence']}")

if __name__ == "__main__":
    main()