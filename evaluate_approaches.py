"""
Comprehensive Evaluation Script for Resume Parsing Approaches
Compares accuracy, performance, and reliability of all implemented methods
"""

import time
import json
import traceback
from typing import Dict, List, Any, Optional
import logging
from datetime import datetime

# Import all parsing approaches
try:
    from txt2Json.apporach1 import RuleBasedResumeParser
    from txt2Json.approach2 import SpacyResumeParser
    from txt2Json.approach3 import MLResumeParser
    from txt2Json.approach4_hybrid import HybridResumeParser
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure all approach files are in the txt2Json directory")

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ResumeParsingEvaluator:
    """
    Evaluates and compares different resume parsing approaches
    """
    
    def __init__(self):
        self.parsers = {}
        self.test_cases = []
        self.results = {}
        self.setup_parsers()
        self.setup_test_cases()
    
    def setup_parsers(self):
        """Initialize all available parsers"""
        logger.info("Setting up parsers...")
        
        # Rule-based parser
        try:
            self.parsers['rule_based'] = RuleBasedResumeParser()
            logger.info("✓ Rule-based parser initialized")
        except Exception as e:
            logger.error(f"✗ Rule-based parser failed: {e}")
        
        # spaCy parser
        try:
            self.parsers['spacy'] = SpacyResumeParser()
            logger.info("✓ spaCy parser initialized")
        except Exception as e:
            logger.warning(f"⚠ spaCy parser not available: {e}")
        
        # ML parser
        try:
            self.parsers['ml'] = MLResumeParser()
            logger.info("✓ ML parser initialized")
        except Exception as e:
            logger.error(f"✗ ML parser failed: {e}")
        
        # Hybrid parser
        try:
            self.parsers['hybrid'] = HybridResumeParser()
            logger.info("✓ Hybrid parser initialized")
        except Exception as e:
            logger.error(f"✗ Hybrid parser failed: {e}")
    
    def setup_test_cases(self):
        """Setup test cases with expected results for evaluation"""
        self.test_cases = [
            {
                "name": "Standard Angular Developer Resume",
                "text": """
                John Doe
                Senior Angular Developer
                <EMAIL>
                +91 9876543210
                Mumbai, India 400001
                
                Professional Experience
                Senior Angular Developer at Tech Solutions Pvt Ltd
                June 2021 - March 2023
                - Developed Angular applications using TypeScript
                - Implemented responsive designs with Bootstrap
                
                Software Engineer at StartupCorp Inc
                January 2019 - May 2021
                - Built React applications
                
                Education
                B.Tech in Computer Science
                IIT Delhi
                2015-2019
                
                Skills
                Angular, TypeScript, JavaScript, React, Node.js, MongoDB
                """,
                "expected": {
                    "name": "John Doe",
                    "email": "<EMAIL>",
                    "phone": "+91 9876543210",
                    "skills_count": 6,
                    "experience_count": 2,
                    "education_count": 1,
                    "address_city": "Mumbai"
                }
            },
            {
                "name": "Minimal Resume",
                "text": """
                Jane Smith
                <EMAIL>
                (*************
                
                Experience:
                Frontend Developer at WebCorp
                2020-2023
                
                Skills: HTML, CSS, JavaScript
                """,
                "expected": {
                    "name": "Jane Smith",
                    "email": "<EMAIL>",
                    "phone": "(*************",
                    "skills_count": 3,
                    "experience_count": 1,
                    "education_count": 0
                }
            },
            {
                "name": "Complex Resume with Multiple Sections",
                "text": """
                Michael Johnson
                Full Stack Developer
                <EMAIL>
                ******-567-8900
                San Francisco, CA, USA
                
                Summary
                Experienced full stack developer with 5+ years in web development
                
                Work Experience
                Senior Full Stack Developer
                Google Inc
                2021 - Present
                - Lead development of microservices architecture
                - Mentored junior developers
                
                Full Stack Developer
                Facebook Inc
                2019 - 2021
                - Developed React applications
                - Built Node.js APIs
                
                Education
                Master of Science in Computer Science
                Stanford University
                2017-2019
                
                Bachelor of Engineering in Software Engineering
                UC Berkeley
                2013-2017
                
                Technical Skills
                Languages: Python, JavaScript, TypeScript, Java, Go
                Frontend: React, Angular, Vue.js, HTML5, CSS3
                Backend: Node.js, Django, Spring Boot, Express
                Databases: PostgreSQL, MongoDB, Redis
                Cloud: AWS, Azure, Docker, Kubernetes
                """,
                "expected": {
                    "name": "Michael Johnson",
                    "email": "<EMAIL>",
                    "phone": "******-567-8900",
                    "skills_count": 15,
                    "experience_count": 2,
                    "education_count": 2,
                    "address_city": "San Francisco"
                }
            }
        ]
    
    def evaluate_all_approaches(self) -> Dict[str, Any]:
        """
        Evaluate all parsing approaches on test cases
        """
        logger.info("Starting comprehensive evaluation...")
        
        evaluation_results = {
            "timestamp": datetime.now().isoformat(),
            "test_cases": len(self.test_cases),
            "parsers_tested": list(self.parsers.keys()),
            "results": {},
            "summary": {}
        }
        
        for parser_name, parser in self.parsers.items():
            logger.info(f"\nEvaluating {parser_name} parser...")
            
            parser_results = {
                "accuracy_scores": {},
                "performance_metrics": {},
                "error_count": 0,
                "test_results": []
            }
            
            for i, test_case in enumerate(self.test_cases):
                logger.info(f"  Testing case {i+1}: {test_case['name']}")
                
                # Measure performance
                start_time = time.time()
                
                try:
                    # Parse the resume
                    result = parser.parse_resume(test_case["text"])
                    
                    # Measure time
                    parse_time = time.time() - start_time
                    
                    # Calculate accuracy
                    accuracy = self.calculate_accuracy(result, test_case["expected"])
                    
                    test_result = {
                        "test_case": test_case["name"],
                        "accuracy": accuracy,
                        "parse_time": round(parse_time, 3),
                        "success": True,
                        "extracted_data": {
                            "name": result.get("name", ""),
                            "email": result.get("email", ""),
                            "phone": result.get("phone", ""),
                            "skills_count": len(result.get("skills", [])),
                            "experience_count": len(result.get("experience", [])),
                            "education_count": len(result.get("education", []))
                        }
                    }
                    
                except Exception as e:
                    logger.error(f"    Error: {str(e)}")
                    parser_results["error_count"] += 1
                    
                    test_result = {
                        "test_case": test_case["name"],
                        "accuracy": 0.0,
                        "parse_time": time.time() - start_time,
                        "success": False,
                        "error": str(e)
                    }
                
                parser_results["test_results"].append(test_result)
            
            # Calculate overall metrics
            successful_tests = [r for r in parser_results["test_results"] if r["success"]]
            
            if successful_tests:
                parser_results["accuracy_scores"]["average"] = round(
                    sum(r["accuracy"] for r in successful_tests) / len(successful_tests), 3
                )
                parser_results["performance_metrics"]["average_time"] = round(
                    sum(r["parse_time"] for r in successful_tests) / len(successful_tests), 3
                )
                parser_results["performance_metrics"]["max_time"] = round(
                    max(r["parse_time"] for r in successful_tests), 3
                )
                parser_results["performance_metrics"]["min_time"] = round(
                    min(r["parse_time"] for r in successful_tests), 3
                )
            else:
                parser_results["accuracy_scores"]["average"] = 0.0
                parser_results["performance_metrics"]["average_time"] = 0.0
            
            parser_results["success_rate"] = len(successful_tests) / len(self.test_cases)
            
            evaluation_results["results"][parser_name] = parser_results
        
        # Generate summary
        evaluation_results["summary"] = self.generate_summary(evaluation_results["results"])
        
        logger.info("\nEvaluation completed!")
        return evaluation_results
    
    def calculate_accuracy(self, result: Dict[str, Any], expected: Dict[str, Any]) -> float:
        """
        Calculate accuracy score by comparing extracted data with expected values
        """
        score = 0.0
        total_checks = 0
        
        # Check name
        if "name" in expected:
            total_checks += 1
            if result.get("name", "").lower() == expected["name"].lower():
                score += 1
        
        # Check email
        if "email" in expected:
            total_checks += 1
            if result.get("email", "") == expected["email"]:
                score += 1
        
        # Check phone
        if "phone" in expected:
            total_checks += 1
            if result.get("phone", "") == expected["phone"]:
                score += 1
        
        # Check skills count (with tolerance)
        if "skills_count" in expected:
            total_checks += 1
            actual_count = len(result.get("skills", []))
            expected_count = expected["skills_count"]
            # Allow ±2 difference in skill count
            if abs(actual_count - expected_count) <= 2:
                score += 1
        
        # Check experience count
        if "experience_count" in expected:
            total_checks += 1
            if len(result.get("experience", [])) >= expected["experience_count"]:
                score += 1
        
        # Check education count
        if "education_count" in expected:
            total_checks += 1
            if len(result.get("education", [])) >= expected["education_count"]:
                score += 1
        
        # Check address city
        if "address_city" in expected:
            total_checks += 1
            actual_city = result.get("address", {}).get("city", "").lower()
            expected_city = expected["address_city"].lower()
            if expected_city in actual_city or actual_city in expected_city:
                score += 1
        
        return round(score / total_checks if total_checks > 0 else 0.0, 3)
    
    def generate_summary(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate summary comparison of all approaches"""
        summary = {
            "best_accuracy": {"parser": "", "score": 0.0},
            "best_performance": {"parser": "", "time": float('inf')},
            "most_reliable": {"parser": "", "success_rate": 0.0},
            "recommendations": []
        }
        
        for parser_name, parser_results in results.items():
            avg_accuracy = parser_results["accuracy_scores"].get("average", 0.0)
            avg_time = parser_results["performance_metrics"].get("average_time", float('inf'))
            success_rate = parser_results.get("success_rate", 0.0)
            
            # Best accuracy
            if avg_accuracy > summary["best_accuracy"]["score"]:
                summary["best_accuracy"] = {"parser": parser_name, "score": avg_accuracy}
            
            # Best performance
            if avg_time < summary["best_performance"]["time"]:
                summary["best_performance"] = {"parser": parser_name, "time": avg_time}
            
            # Most reliable
            if success_rate > summary["most_reliable"]["success_rate"]:
                summary["most_reliable"] = {"parser": parser_name, "success_rate": success_rate}
        
        # Generate recommendations
        if summary["best_accuracy"]["score"] > 0.8:
            summary["recommendations"].append(f"Use {summary['best_accuracy']['parser']} for highest accuracy")
        
        if summary["best_performance"]["time"] < 1.0:
            summary["recommendations"].append(f"Use {summary['best_performance']['parser']} for fastest processing")
        
        if summary["most_reliable"]["success_rate"] == 1.0:
            summary["recommendations"].append(f"Use {summary['most_reliable']['parser']} for most reliable results")
        
        return summary
    
    def save_results(self, results: Dict[str, Any], filename: str = "evaluation_results.json"):
        """Save evaluation results to file"""
        with open(filename, 'w') as f:
            json.dump(results, f, indent=2)
        logger.info(f"Results saved to {filename}")
    
    def print_summary(self, results: Dict[str, Any]):
        """Print a formatted summary of results"""
        print("\n" + "="*80)
        print("RESUME PARSING APPROACHES - EVALUATION SUMMARY")
        print("="*80)
        
        summary = results["summary"]
        
        print(f"\n📊 BEST PERFORMERS:")
        print(f"   🎯 Highest Accuracy: {summary['best_accuracy']['parser']} ({summary['best_accuracy']['score']:.1%})")
        print(f"   ⚡ Fastest Processing: {summary['best_performance']['parser']} ({summary['best_performance']['time']:.3f}s)")
        print(f"   🛡️  Most Reliable: {summary['most_reliable']['parser']} ({summary['most_reliable']['success_rate']:.1%})")
        
        print(f"\n📈 DETAILED RESULTS:")
        for parser_name, parser_results in results["results"].items():
            print(f"\n   {parser_name.upper()}:")
            print(f"      Accuracy: {parser_results['accuracy_scores'].get('average', 0):.1%}")
            print(f"      Avg Time: {parser_results['performance_metrics'].get('average_time', 0):.3f}s")
            print(f"      Success Rate: {parser_results.get('success_rate', 0):.1%}")
            print(f"      Errors: {parser_results.get('error_count', 0)}")
        
        print(f"\n💡 RECOMMENDATIONS:")
        for rec in summary["recommendations"]:
            print(f"   • {rec}")
        
        print("\n" + "="*80)

def main():
    """Main evaluation function"""
    evaluator = ResumeParsingEvaluator()
    results = evaluator.evaluate_all_approaches()
    
    # Print summary
    evaluator.print_summary(results)
    
    # Save detailed results
    evaluator.save_results(results)
    
    return results

if __name__ == "__main__":
    main()
